<?xml version="1.0"?>
<launch>
  <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
  <set_env name="PARAM_SERVER_PORT" value="5055" />
  <set_env name="PARAM_SERVER_LOGLEVEL" value="info" />

  <arg name="log_level" default="info" description="Logging level for the arm_controller node"/>

  <node pkg="arm_controller" exec="arm_controller" name="arm_controller" output="screen">
    <param name="use_sim_time" value="false"/>
    <param name="log_level" value="$(var log_level)"/>
  </node>
</launch>


