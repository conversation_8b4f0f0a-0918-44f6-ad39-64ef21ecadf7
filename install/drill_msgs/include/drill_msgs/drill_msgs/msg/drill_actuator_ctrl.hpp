// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef DRILL_MSGS__MSG__DRILL_ACTUATOR_CTRL_HPP_
#define DRILL_MSGS__MSG__DRILL_ACTUATOR_CTRL_HPP_

#include "drill_msgs/msg/detail/drill_actuator_ctrl__struct.hpp"
#include "drill_msgs/msg/detail/drill_actuator_ctrl__builder.hpp"
#include "drill_msgs/msg/detail/drill_actuator_ctrl__traits.hpp"
#include "drill_msgs/msg/detail/drill_actuator_ctrl__type_support.hpp"

#endif  // DRILL_MSGS__MSG__DRILL_ACTUATOR_CTRL_HPP_
