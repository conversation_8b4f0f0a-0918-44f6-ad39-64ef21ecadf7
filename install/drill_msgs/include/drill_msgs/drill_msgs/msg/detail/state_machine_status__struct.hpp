// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/StateMachineStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/state_machine_status.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__StateMachineStatus __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__StateMachineStatus __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct StateMachineStatus_
{
  using Type = StateMachineStatus_<ContainerAllocator>;

  explicit StateMachineStatus_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->cur_action_id = 0l;
      this->last_action_id = 0l;
      this->current_state = "";
    }
  }

  explicit StateMachineStatus_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init),
    current_state(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->cur_action_id = 0l;
      this->last_action_id = 0l;
      this->current_state = "";
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _cur_action_id_type =
    int32_t;
  _cur_action_id_type cur_action_id;
  using _last_action_id_type =
    int32_t;
  _last_action_id_type last_action_id;
  using _current_state_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _current_state_type current_state;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__cur_action_id(
    const int32_t & _arg)
  {
    this->cur_action_id = _arg;
    return *this;
  }
  Type & set__last_action_id(
    const int32_t & _arg)
  {
    this->last_action_id = _arg;
    return *this;
  }
  Type & set__current_state(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->current_state = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::StateMachineStatus_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::StateMachineStatus_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::StateMachineStatus_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::StateMachineStatus_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__StateMachineStatus
    std::shared_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__StateMachineStatus
    std::shared_ptr<drill_msgs::msg::StateMachineStatus_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const StateMachineStatus_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->cur_action_id != other.cur_action_id) {
      return false;
    }
    if (this->last_action_id != other.last_action_id) {
      return false;
    }
    if (this->current_state != other.current_state) {
      return false;
    }
    return true;
  }
  bool operator!=(const StateMachineStatus_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct StateMachineStatus_

// alias to use template instance with default allocator
using StateMachineStatus =
  drill_msgs::msg::StateMachineStatus_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__STRUCT_HPP_
