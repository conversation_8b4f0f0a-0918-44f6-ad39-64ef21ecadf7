// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/WrenchCtrl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/wrench_ctrl.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__WRENCH_CTRL__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__WRENCH_CTRL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/wrench_ctrl__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_WrenchCtrl_grip
{
public:
  explicit Init_WrenchCtrl_grip(::drill_msgs::msg::WrenchCtrl & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::WrenchCtrl grip(::drill_msgs::msg::WrenchCtrl::_grip_type arg)
  {
    msg_.grip = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::WrenchCtrl msg_;
};

class Init_WrenchCtrl_swing
{
public:
  explicit Init_WrenchCtrl_swing(::drill_msgs::msg::WrenchCtrl & msg)
  : msg_(msg)
  {}
  Init_WrenchCtrl_grip swing(::drill_msgs::msg::WrenchCtrl::_swing_type arg)
  {
    msg_.swing = std::move(arg);
    return Init_WrenchCtrl_grip(msg_);
  }

private:
  ::drill_msgs::msg::WrenchCtrl msg_;
};

class Init_WrenchCtrl_header
{
public:
  Init_WrenchCtrl_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_WrenchCtrl_swing header(::drill_msgs::msg::WrenchCtrl::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_WrenchCtrl_swing(msg_);
  }

private:
  ::drill_msgs::msg::WrenchCtrl msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::WrenchCtrl>()
{
  return drill_msgs::msg::builder::Init_WrenchCtrl_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__WRENCH_CTRL__BUILDER_HPP_
