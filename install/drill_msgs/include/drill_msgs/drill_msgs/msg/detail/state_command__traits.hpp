// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/StateCommand.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/state_command.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__STATE_COMMAND__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__STATE_COMMAND__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/state_command__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const StateCommand & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: node_name
  {
    out << "node_name: ";
    rosidl_generator_traits::value_to_yaml(msg.node_name, out);
    out << ", ";
  }

  // member: state
  {
    out << "state: ";
    rosidl_generator_traits::value_to_yaml(msg.state, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const StateCommand & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: node_name
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "node_name: ";
    rosidl_generator_traits::value_to_yaml(msg.node_name, out);
    out << "\n";
  }

  // member: state
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "state: ";
    rosidl_generator_traits::value_to_yaml(msg.state, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const StateCommand & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::StateCommand & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::StateCommand & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::StateCommand>()
{
  return "drill_msgs::msg::StateCommand";
}

template<>
inline const char * name<drill_msgs::msg::StateCommand>()
{
  return "drill_msgs/msg/StateCommand";
}

template<>
struct has_fixed_size<drill_msgs::msg::StateCommand>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<drill_msgs::msg::StateCommand>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<drill_msgs::msg::StateCommand>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__STATE_COMMAND__TRAITS_HPP_
