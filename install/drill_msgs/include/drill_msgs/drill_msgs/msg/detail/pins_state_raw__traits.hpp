// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/PinsStateRaw.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/pins_state_raw.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__PINS_STATE_RAW__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__PINS_STATE_RAW__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/pins_state_raw__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const PinsStateRaw & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: left_open
  {
    out << "left_open: ";
    rosidl_generator_traits::value_to_yaml(msg.left_open, out);
    out << ", ";
  }

  // member: left_locked
  {
    out << "left_locked: ";
    rosidl_generator_traits::value_to_yaml(msg.left_locked, out);
    out << ", ";
  }

  // member: right_open
  {
    out << "right_open: ";
    rosidl_generator_traits::value_to_yaml(msg.right_open, out);
    out << ", ";
  }

  // member: right_locked
  {
    out << "right_locked: ";
    rosidl_generator_traits::value_to_yaml(msg.right_locked, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PinsStateRaw & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: left_open
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "left_open: ";
    rosidl_generator_traits::value_to_yaml(msg.left_open, out);
    out << "\n";
  }

  // member: left_locked
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "left_locked: ";
    rosidl_generator_traits::value_to_yaml(msg.left_locked, out);
    out << "\n";
  }

  // member: right_open
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_open: ";
    rosidl_generator_traits::value_to_yaml(msg.right_open, out);
    out << "\n";
  }

  // member: right_locked
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_locked: ";
    rosidl_generator_traits::value_to_yaml(msg.right_locked, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PinsStateRaw & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::PinsStateRaw & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::PinsStateRaw & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::PinsStateRaw>()
{
  return "drill_msgs::msg::PinsStateRaw";
}

template<>
inline const char * name<drill_msgs::msg::PinsStateRaw>()
{
  return "drill_msgs/msg/PinsStateRaw";
}

template<>
struct has_fixed_size<drill_msgs::msg::PinsStateRaw>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<drill_msgs::msg::PinsStateRaw>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<drill_msgs::msg::PinsStateRaw>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__PINS_STATE_RAW__TRAITS_HPP_
