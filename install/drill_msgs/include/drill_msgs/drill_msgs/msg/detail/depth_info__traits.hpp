// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/DepthInfo.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/depth_info.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__DEPTH_INFO__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__DEPTH_INFO__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/depth_info__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const DepthInfo & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: hole_depth
  {
    out << "hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.hole_depth, out);
    out << ", ";
  }

  // member: current_bit_depth
  {
    out << "current_bit_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.current_bit_depth, out);
    out << ", ";
  }

  // member: tvd_hole_depth
  {
    out << "tvd_hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.tvd_hole_depth, out);
    out << ", ";
  }

  // member: head_pos
  {
    out << "head_pos: ";
    rosidl_generator_traits::value_to_yaml(msg.head_pos, out);
    out << ", ";
  }

  // member: ground_head_pos
  {
    out << "ground_head_pos: ";
    rosidl_generator_traits::value_to_yaml(msg.ground_head_pos, out);
    out << ", ";
  }

  // member: wellhead_altitude
  {
    out << "wellhead_altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_altitude, out);
    out << ", ";
  }

  // member: hole_inclination
  {
    out << "hole_inclination: ";
    rosidl_generator_traits::value_to_yaml(msg.hole_inclination, out);
    out << ", ";
  }

  // member: target_hole_depth
  {
    out << "target_hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.target_hole_depth, out);
    out << ", ";
  }

  // member: wellhead_x
  {
    out << "wellhead_x: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_x, out);
    out << ", ";
  }

  // member: wellhead_y
  {
    out << "wellhead_y: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_y, out);
    out << ", ";
  }

  // member: tracking_active
  {
    out << "tracking_active: ";
    rosidl_generator_traits::value_to_yaml(msg.tracking_active, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const DepthInfo & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: hole_depth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.hole_depth, out);
    out << "\n";
  }

  // member: current_bit_depth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "current_bit_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.current_bit_depth, out);
    out << "\n";
  }

  // member: tvd_hole_depth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "tvd_hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.tvd_hole_depth, out);
    out << "\n";
  }

  // member: head_pos
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "head_pos: ";
    rosidl_generator_traits::value_to_yaml(msg.head_pos, out);
    out << "\n";
  }

  // member: ground_head_pos
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "ground_head_pos: ";
    rosidl_generator_traits::value_to_yaml(msg.ground_head_pos, out);
    out << "\n";
  }

  // member: wellhead_altitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "wellhead_altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_altitude, out);
    out << "\n";
  }

  // member: hole_inclination
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "hole_inclination: ";
    rosidl_generator_traits::value_to_yaml(msg.hole_inclination, out);
    out << "\n";
  }

  // member: target_hole_depth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "target_hole_depth: ";
    rosidl_generator_traits::value_to_yaml(msg.target_hole_depth, out);
    out << "\n";
  }

  // member: wellhead_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "wellhead_x: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_x, out);
    out << "\n";
  }

  // member: wellhead_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "wellhead_y: ";
    rosidl_generator_traits::value_to_yaml(msg.wellhead_y, out);
    out << "\n";
  }

  // member: tracking_active
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "tracking_active: ";
    rosidl_generator_traits::value_to_yaml(msg.tracking_active, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const DepthInfo & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::DepthInfo & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::DepthInfo & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::DepthInfo>()
{
  return "drill_msgs::msg::DepthInfo";
}

template<>
inline const char * name<drill_msgs::msg::DepthInfo>()
{
  return "drill_msgs/msg/DepthInfo";
}

template<>
struct has_fixed_size<drill_msgs::msg::DepthInfo>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<drill_msgs::msg::DepthInfo>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<drill_msgs::msg::DepthInfo>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__DEPTH_INFO__TRAITS_HPP_
