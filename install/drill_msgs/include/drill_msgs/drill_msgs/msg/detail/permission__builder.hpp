// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/Permission.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/permission.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__PERMISSION__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__PERMISSION__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/permission__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_Permission_source
{
public:
  explicit Init_Permission_source(::drill_msgs::msg::Permission & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::Permission source(::drill_msgs::msg::Permission::_source_type arg)
  {
    msg_.source = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::Permission msg_;
};

class Init_Permission_permission
{
public:
  explicit Init_Permission_permission(::drill_msgs::msg::Permission & msg)
  : msg_(msg)
  {}
  Init_Permission_source permission(::drill_msgs::msg::Permission::_permission_type arg)
  {
    msg_.permission = std::move(arg);
    return Init_Permission_source(msg_);
  }

private:
  ::drill_msgs::msg::Permission msg_;
};

class Init_Permission_header
{
public:
  Init_Permission_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Permission_permission header(::drill_msgs::msg::Permission::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_Permission_permission(msg_);
  }

private:
  ::drill_msgs::msg::Permission msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::Permission>()
{
  return drill_msgs::msg::builder::Init_Permission_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__PERMISSION__BUILDER_HPP_
