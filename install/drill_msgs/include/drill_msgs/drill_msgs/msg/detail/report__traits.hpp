// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/Report.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/report.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__REPORT__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__REPORT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/report__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const Report & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: source
  {
    out << "source: ";
    rosidl_generator_traits::value_to_yaml(msg.source, out);
    out << ", ";
  }

  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Report & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: source
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "source: ";
    rosidl_generator_traits::value_to_yaml(msg.source, out);
    out << "\n";
  }

  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Report & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::Report & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::Report & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::Report>()
{
  return "drill_msgs::msg::Report";
}

template<>
inline const char * name<drill_msgs::msg::Report>()
{
  return "drill_msgs/msg/Report";
}

template<>
struct has_fixed_size<drill_msgs::msg::Report>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<drill_msgs::msg::Report>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<drill_msgs::msg::Report>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__REPORT__TRAITS_HPP_
