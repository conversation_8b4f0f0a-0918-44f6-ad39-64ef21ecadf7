// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/Path.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/path.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__PATH__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__PATH__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/path__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_Path_is_backward
{
public:
  explicit Init_Path_is_backward(::drill_msgs::msg::Path & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::Path is_backward(::drill_msgs::msg::Path::_is_backward_type arg)
  {
    msg_.is_backward = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::Path msg_;
};

class Init_Path_points
{
public:
  Init_Path_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Path_is_backward points(::drill_msgs::msg::Path::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Path_is_backward(msg_);
  }

private:
  ::drill_msgs::msg::Path msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::Path>()
{
  return drill_msgs::msg::builder::Init_Path_points();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__PATH__BUILDER_HPP_
