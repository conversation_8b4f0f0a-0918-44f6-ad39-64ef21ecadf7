// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/TowerState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/tower_state.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__TOWER_STATE__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__TOWER_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__TowerState __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__TowerState __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct TowerState_
{
  using Type = TowerState_<ContainerAllocator>;

  explicit TowerState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->inclination = 0.0f;
      this->vert_pin_locked = false;
      this->vert_pin_unlocked = false;
      this->incl_pin_locked = false;
      this->incl_pin_unlocked = false;
    }
  }

  explicit TowerState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->inclination = 0.0f;
      this->vert_pin_locked = false;
      this->vert_pin_unlocked = false;
      this->incl_pin_locked = false;
      this->incl_pin_unlocked = false;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _inclination_type =
    float;
  _inclination_type inclination;
  using _vert_pin_locked_type =
    bool;
  _vert_pin_locked_type vert_pin_locked;
  using _vert_pin_unlocked_type =
    bool;
  _vert_pin_unlocked_type vert_pin_unlocked;
  using _incl_pin_locked_type =
    bool;
  _incl_pin_locked_type incl_pin_locked;
  using _incl_pin_unlocked_type =
    bool;
  _incl_pin_unlocked_type incl_pin_unlocked;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__inclination(
    const float & _arg)
  {
    this->inclination = _arg;
    return *this;
  }
  Type & set__vert_pin_locked(
    const bool & _arg)
  {
    this->vert_pin_locked = _arg;
    return *this;
  }
  Type & set__vert_pin_unlocked(
    const bool & _arg)
  {
    this->vert_pin_unlocked = _arg;
    return *this;
  }
  Type & set__incl_pin_locked(
    const bool & _arg)
  {
    this->incl_pin_locked = _arg;
    return *this;
  }
  Type & set__incl_pin_unlocked(
    const bool & _arg)
  {
    this->incl_pin_unlocked = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::TowerState_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::TowerState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::TowerState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::TowerState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::TowerState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::TowerState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::TowerState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::TowerState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::TowerState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::TowerState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__TowerState
    std::shared_ptr<drill_msgs::msg::TowerState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__TowerState
    std::shared_ptr<drill_msgs::msg::TowerState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const TowerState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->inclination != other.inclination) {
      return false;
    }
    if (this->vert_pin_locked != other.vert_pin_locked) {
      return false;
    }
    if (this->vert_pin_unlocked != other.vert_pin_unlocked) {
      return false;
    }
    if (this->incl_pin_locked != other.incl_pin_locked) {
      return false;
    }
    if (this->incl_pin_unlocked != other.incl_pin_unlocked) {
      return false;
    }
    return true;
  }
  bool operator!=(const TowerState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct TowerState_

// alias to use template instance with default allocator
using TowerState =
  drill_msgs::msg::TowerState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__TOWER_STATE__STRUCT_HPP_
