// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/UpsStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/ups_status.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__UPS_STATUS__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__UPS_STATUS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/ups_status__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_UpsStatus_grid_current_exceeded
{
public:
  explicit Init_UpsStatus_grid_current_exceeded(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::UpsStatus grid_current_exceeded(::drill_msgs::msg::UpsStatus::_grid_current_exceeded_type arg)
  {
    msg_.grid_current_exceeded = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_batt_current_exceeded
{
public:
  explicit Init_UpsStatus_batt_current_exceeded(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_grid_current_exceeded batt_current_exceeded(::drill_msgs::msg::UpsStatus::_batt_current_exceeded_type arg)
  {
    msg_.batt_current_exceeded = std::move(arg);
    return Init_UpsStatus_grid_current_exceeded(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_grid_voltage_ok
{
public:
  explicit Init_UpsStatus_grid_voltage_ok(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_batt_current_exceeded grid_voltage_ok(::drill_msgs::msg::UpsStatus::_grid_voltage_ok_type arg)
  {
    msg_.grid_voltage_ok = std::move(arg);
    return Init_UpsStatus_batt_current_exceeded(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_batt_voltage_ok
{
public:
  explicit Init_UpsStatus_batt_voltage_ok(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_grid_voltage_ok batt_voltage_ok(::drill_msgs::msg::UpsStatus::_batt_voltage_ok_type arg)
  {
    msg_.batt_voltage_ok = std::move(arg);
    return Init_UpsStatus_grid_voltage_ok(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_grid_active
{
public:
  explicit Init_UpsStatus_grid_active(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_batt_voltage_ok grid_active(::drill_msgs::msg::UpsStatus::_grid_active_type arg)
  {
    msg_.grid_active = std::move(arg);
    return Init_UpsStatus_batt_voltage_ok(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_batt_active
{
public:
  explicit Init_UpsStatus_batt_active(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_grid_active batt_active(::drill_msgs::msg::UpsStatus::_batt_active_type arg)
  {
    msg_.batt_active = std::move(arg);
    return Init_UpsStatus_grid_active(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_charge_current
{
public:
  explicit Init_UpsStatus_charge_current(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_batt_active charge_current(::drill_msgs::msg::UpsStatus::_charge_current_type arg)
  {
    msg_.charge_current = std::move(arg);
    return Init_UpsStatus_batt_active(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_grid_voltage
{
public:
  explicit Init_UpsStatus_grid_voltage(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_charge_current grid_voltage(::drill_msgs::msg::UpsStatus::_grid_voltage_type arg)
  {
    msg_.grid_voltage = std::move(arg);
    return Init_UpsStatus_charge_current(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_batt_voltage
{
public:
  explicit Init_UpsStatus_batt_voltage(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_grid_voltage batt_voltage(::drill_msgs::msg::UpsStatus::_batt_voltage_type arg)
  {
    msg_.batt_voltage = std::move(arg);
    return Init_UpsStatus_grid_voltage(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_grid_current
{
public:
  explicit Init_UpsStatus_grid_current(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_batt_voltage grid_current(::drill_msgs::msg::UpsStatus::_grid_current_type arg)
  {
    msg_.grid_current = std::move(arg);
    return Init_UpsStatus_batt_voltage(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_batt_current
{
public:
  explicit Init_UpsStatus_batt_current(::drill_msgs::msg::UpsStatus & msg)
  : msg_(msg)
  {}
  Init_UpsStatus_grid_current batt_current(::drill_msgs::msg::UpsStatus::_batt_current_type arg)
  {
    msg_.batt_current = std::move(arg);
    return Init_UpsStatus_grid_current(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

class Init_UpsStatus_header
{
public:
  Init_UpsStatus_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_UpsStatus_batt_current header(::drill_msgs::msg::UpsStatus::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_UpsStatus_batt_current(msg_);
  }

private:
  ::drill_msgs::msg::UpsStatus msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::UpsStatus>()
{
  return drill_msgs::msg::builder::Init_UpsStatus_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__UPS_STATUS__BUILDER_HPP_
