// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/MainAction.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/main_action.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__MAIN_ACTION__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__MAIN_ACTION__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/main_action__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_MainAction_drilled_holes
{
public:
  explicit Init_MainAction_drilled_holes(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::MainAction drilled_holes(::drill_msgs::msg::MainAction::_drilled_holes_type arg)
  {
    msg_.drilled_holes = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_borders
{
public:
  explicit Init_MainAction_borders(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_drilled_holes borders(::drill_msgs::msg::MainAction::_borders_type arg)
  {
    msg_.borders = std::move(arg);
    return Init_MainAction_drilled_holes(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_no_auto_move
{
public:
  explicit Init_MainAction_no_auto_move(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_borders no_auto_move(::drill_msgs::msg::MainAction::_no_auto_move_type arg)
  {
    msg_.no_auto_move = std::move(arg);
    return Init_MainAction_borders(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_is_edge
{
public:
  explicit Init_MainAction_is_edge(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_no_auto_move is_edge(::drill_msgs::msg::MainAction::_is_edge_type arg)
  {
    msg_.is_edge = std::move(arg);
    return Init_MainAction_no_auto_move(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_depth
{
public:
  explicit Init_MainAction_depth(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_is_edge depth(::drill_msgs::msg::MainAction::_depth_type arg)
  {
    msg_.depth = std::move(arg);
    return Init_MainAction_is_edge(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_inclination
{
public:
  explicit Init_MainAction_inclination(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_depth inclination(::drill_msgs::msg::MainAction::_inclination_type arg)
  {
    msg_.inclination = std::move(arg);
    return Init_MainAction_depth(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_azimuth
{
public:
  explicit Init_MainAction_azimuth(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_inclination azimuth(::drill_msgs::msg::MainAction::_azimuth_type arg)
  {
    msg_.azimuth = std::move(arg);
    return Init_MainAction_inclination(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_z
{
public:
  explicit Init_MainAction_z(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_azimuth z(::drill_msgs::msg::MainAction::_z_type arg)
  {
    msg_.z = std::move(arg);
    return Init_MainAction_azimuth(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_y
{
public:
  explicit Init_MainAction_y(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_z y(::drill_msgs::msg::MainAction::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_MainAction_z(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_x
{
public:
  explicit Init_MainAction_x(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_y x(::drill_msgs::msg::MainAction::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_MainAction_y(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_id
{
public:
  explicit Init_MainAction_id(::drill_msgs::msg::MainAction & msg)
  : msg_(msg)
  {}
  Init_MainAction_x id(::drill_msgs::msg::MainAction::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_MainAction_x(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

class Init_MainAction_header
{
public:
  Init_MainAction_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_MainAction_id header(::drill_msgs::msg::MainAction::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_MainAction_id(msg_);
  }

private:
  ::drill_msgs::msg::MainAction msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::MainAction>()
{
  return drill_msgs::msg::builder::Init_MainAction_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__MAIN_ACTION__BUILDER_HPP_
