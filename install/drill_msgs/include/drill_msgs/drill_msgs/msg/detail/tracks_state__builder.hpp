// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/TracksState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/tracks_state.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__TRACKS_STATE__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__TRACKS_STATE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/tracks_state__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_TracksState_is_reliable
{
public:
  explicit Init_TracksState_is_reliable(::drill_msgs::msg::TracksState & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::TracksState is_reliable(::drill_msgs::msg::TracksState::_is_reliable_type arg)
  {
    msg_.is_reliable = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::TracksState msg_;
};

class Init_TracksState_right
{
public:
  explicit Init_TracksState_right(::drill_msgs::msg::TracksState & msg)
  : msg_(msg)
  {}
  Init_TracksState_is_reliable right(::drill_msgs::msg::TracksState::_right_type arg)
  {
    msg_.right = std::move(arg);
    return Init_TracksState_is_reliable(msg_);
  }

private:
  ::drill_msgs::msg::TracksState msg_;
};

class Init_TracksState_left
{
public:
  explicit Init_TracksState_left(::drill_msgs::msg::TracksState & msg)
  : msg_(msg)
  {}
  Init_TracksState_right left(::drill_msgs::msg::TracksState::_left_type arg)
  {
    msg_.left = std::move(arg);
    return Init_TracksState_right(msg_);
  }

private:
  ::drill_msgs::msg::TracksState msg_;
};

class Init_TracksState_header
{
public:
  Init_TracksState_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_TracksState_left header(::drill_msgs::msg::TracksState::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_TracksState_left(msg_);
  }

private:
  ::drill_msgs::msg::TracksState msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::TracksState>()
{
  return drill_msgs::msg::builder::Init_TracksState_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__TRACKS_STATE__BUILDER_HPP_
