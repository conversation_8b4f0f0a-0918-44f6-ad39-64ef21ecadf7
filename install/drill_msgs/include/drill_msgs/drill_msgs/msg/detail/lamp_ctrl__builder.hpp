// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/LampCtrl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/lamp_ctrl.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__LAMP_CTRL__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__LAMP_CTRL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/lamp_ctrl__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_LampCtrl_rear_blue
{
public:
  explicit Init_LampCtrl_rear_blue(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::Lamp<PERSON>trl rear_blue(::drill_msgs::msg::LampCtrl::_rear_blue_type arg)
  {
    msg_.rear_blue = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_rear_yellow
{
public:
  explicit Init_LampCtrl_rear_yellow(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  Init_LampCtrl_rear_blue rear_yellow(::drill_msgs::msg::LampCtrl::_rear_yellow_type arg)
  {
    msg_.rear_yellow = std::move(arg);
    return Init_LampCtrl_rear_blue(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_rear_red
{
public:
  explicit Init_LampCtrl_rear_red(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  Init_LampCtrl_rear_yellow rear_red(::drill_msgs::msg::LampCtrl::_rear_red_type arg)
  {
    msg_.rear_red = std::move(arg);
    return Init_LampCtrl_rear_yellow(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_front_blue
{
public:
  explicit Init_LampCtrl_front_blue(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  Init_LampCtrl_rear_red front_blue(::drill_msgs::msg::LampCtrl::_front_blue_type arg)
  {
    msg_.front_blue = std::move(arg);
    return Init_LampCtrl_rear_red(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_front_yellow
{
public:
  explicit Init_LampCtrl_front_yellow(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  Init_LampCtrl_front_blue front_yellow(::drill_msgs::msg::LampCtrl::_front_yellow_type arg)
  {
    msg_.front_yellow = std::move(arg);
    return Init_LampCtrl_front_blue(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_front_red
{
public:
  explicit Init_LampCtrl_front_red(::drill_msgs::msg::LampCtrl & msg)
  : msg_(msg)
  {}
  Init_LampCtrl_front_yellow front_red(::drill_msgs::msg::LampCtrl::_front_red_type arg)
  {
    msg_.front_red = std::move(arg);
    return Init_LampCtrl_front_yellow(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

class Init_LampCtrl_header
{
public:
  Init_LampCtrl_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_LampCtrl_front_red header(::drill_msgs::msg::LampCtrl::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_LampCtrl_front_red(msg_);
  }

private:
  ::drill_msgs::msg::LampCtrl msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::LampCtrl>()
{
  return drill_msgs::msg::builder::Init_LampCtrl_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__LAMP_CTRL__BUILDER_HPP_
