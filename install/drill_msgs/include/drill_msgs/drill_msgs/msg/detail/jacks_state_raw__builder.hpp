// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/JacksStateRaw.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/jacks_state_raw.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/jacks_state_raw__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_JacksStateRaw_rear_right_len
{
public:
  explicit Init_JacksStateRaw_rear_right_len(::drill_msgs::msg::JacksStateRaw & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::JacksStateRaw rear_right_len(::drill_msgs::msg::JacksStateRaw::_rear_right_len_type arg)
  {
    msg_.rear_right_len = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::JacksStateRaw msg_;
};

class Init_JacksStateRaw_rear_left_len
{
public:
  explicit Init_JacksStateRaw_rear_left_len(::drill_msgs::msg::JacksStateRaw & msg)
  : msg_(msg)
  {}
  Init_JacksStateRaw_rear_right_len rear_left_len(::drill_msgs::msg::JacksStateRaw::_rear_left_len_type arg)
  {
    msg_.rear_left_len = std::move(arg);
    return Init_JacksStateRaw_rear_right_len(msg_);
  }

private:
  ::drill_msgs::msg::JacksStateRaw msg_;
};

class Init_JacksStateRaw_front_right_len
{
public:
  explicit Init_JacksStateRaw_front_right_len(::drill_msgs::msg::JacksStateRaw & msg)
  : msg_(msg)
  {}
  Init_JacksStateRaw_rear_left_len front_right_len(::drill_msgs::msg::JacksStateRaw::_front_right_len_type arg)
  {
    msg_.front_right_len = std::move(arg);
    return Init_JacksStateRaw_rear_left_len(msg_);
  }

private:
  ::drill_msgs::msg::JacksStateRaw msg_;
};

class Init_JacksStateRaw_front_left_len
{
public:
  explicit Init_JacksStateRaw_front_left_len(::drill_msgs::msg::JacksStateRaw & msg)
  : msg_(msg)
  {}
  Init_JacksStateRaw_front_right_len front_left_len(::drill_msgs::msg::JacksStateRaw::_front_left_len_type arg)
  {
    msg_.front_left_len = std::move(arg);
    return Init_JacksStateRaw_front_right_len(msg_);
  }

private:
  ::drill_msgs::msg::JacksStateRaw msg_;
};

class Init_JacksStateRaw_header
{
public:
  Init_JacksStateRaw_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JacksStateRaw_front_left_len header(::drill_msgs::msg::JacksStateRaw::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_JacksStateRaw_front_left_len(msg_);
  }

private:
  ::drill_msgs::msg::JacksStateRaw msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::JacksStateRaw>()
{
  return drill_msgs::msg::builder::Init_JacksStateRaw_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__BUILDER_HPP_
