// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/ForkState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/fork_state.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__FORK_STATE__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__FORK_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__ForkState __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__ForkState __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct ForkState_
{
  using Type = ForkState_<ContainerAllocator>;

  explicit ForkState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->open = false;
      this->closed = false;
      this->cw_limit = false;
      this->ccw_limit = false;
    }
  }

  explicit ForkState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->open = false;
      this->closed = false;
      this->cw_limit = false;
      this->ccw_limit = false;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _open_type =
    bool;
  _open_type open;
  using _closed_type =
    bool;
  _closed_type closed;
  using _cw_limit_type =
    bool;
  _cw_limit_type cw_limit;
  using _ccw_limit_type =
    bool;
  _ccw_limit_type ccw_limit;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__open(
    const bool & _arg)
  {
    this->open = _arg;
    return *this;
  }
  Type & set__closed(
    const bool & _arg)
  {
    this->closed = _arg;
    return *this;
  }
  Type & set__cw_limit(
    const bool & _arg)
  {
    this->cw_limit = _arg;
    return *this;
  }
  Type & set__ccw_limit(
    const bool & _arg)
  {
    this->ccw_limit = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::ForkState_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::ForkState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::ForkState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::ForkState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::ForkState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::ForkState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::ForkState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::ForkState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::ForkState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::ForkState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__ForkState
    std::shared_ptr<drill_msgs::msg::ForkState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__ForkState
    std::shared_ptr<drill_msgs::msg::ForkState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const ForkState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->open != other.open) {
      return false;
    }
    if (this->closed != other.closed) {
      return false;
    }
    if (this->cw_limit != other.cw_limit) {
      return false;
    }
    if (this->ccw_limit != other.ccw_limit) {
      return false;
    }
    return true;
  }
  bool operator!=(const ForkState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct ForkState_

// alias to use template instance with default allocator
using ForkState =
  drill_msgs::msg::ForkState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__FORK_STATE__STRUCT_HPP_
