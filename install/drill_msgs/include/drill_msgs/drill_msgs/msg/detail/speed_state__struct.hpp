// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/SpeedState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/speed_state.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__SPEED_STATE__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__SPEED_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__SpeedState __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__SpeedState __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct SpeedState_
{
  using Type = SpeedState_<ContainerAllocator>;

  explicit SpeedState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->forward = 0.0f;
      this->lateral = 0.0f;
      this->angular = 0.0f;
      this->is_reliable = false;
    }
  }

  explicit SpeedState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->forward = 0.0f;
      this->lateral = 0.0f;
      this->angular = 0.0f;
      this->is_reliable = false;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _forward_type =
    float;
  _forward_type forward;
  using _lateral_type =
    float;
  _lateral_type lateral;
  using _angular_type =
    float;
  _angular_type angular;
  using _is_reliable_type =
    bool;
  _is_reliable_type is_reliable;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__forward(
    const float & _arg)
  {
    this->forward = _arg;
    return *this;
  }
  Type & set__lateral(
    const float & _arg)
  {
    this->lateral = _arg;
    return *this;
  }
  Type & set__angular(
    const float & _arg)
  {
    this->angular = _arg;
    return *this;
  }
  Type & set__is_reliable(
    const bool & _arg)
  {
    this->is_reliable = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::SpeedState_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::SpeedState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::SpeedState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::SpeedState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__SpeedState
    std::shared_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__SpeedState
    std::shared_ptr<drill_msgs::msg::SpeedState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const SpeedState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->forward != other.forward) {
      return false;
    }
    if (this->lateral != other.lateral) {
      return false;
    }
    if (this->angular != other.angular) {
      return false;
    }
    if (this->is_reliable != other.is_reliable) {
      return false;
    }
    return true;
  }
  bool operator!=(const SpeedState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct SpeedState_

// alias to use template instance with default allocator
using SpeedState =
  drill_msgs::msg::SpeedState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__SPEED_STATE__STRUCT_HPP_
