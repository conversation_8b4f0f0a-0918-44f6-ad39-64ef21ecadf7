// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/ModeCtrl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/mode_ctrl.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__MODE_CTRL__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__MODE_CTRL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/mode_ctrl__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_ModeCtrl_drive
{
public:
  explicit Init_ModeCtrl_drive(::drill_msgs::msg::ModeCtrl & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::ModeCtrl drive(::drill_msgs::msg::ModeCtrl::_drive_type arg)
  {
    msg_.drive = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::ModeCtrl msg_;
};

class Init_ModeCtrl_header
{
public:
  Init_ModeCtrl_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_ModeCtrl_drive header(::drill_msgs::msg::ModeCtrl::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_ModeCtrl_drive(msg_);
  }

private:
  ::drill_msgs::msg::ModeCtrl msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::ModeCtrl>()
{
  return drill_msgs::msg::builder::Init_ModeCtrl_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__MODE_CTRL__BUILDER_HPP_
