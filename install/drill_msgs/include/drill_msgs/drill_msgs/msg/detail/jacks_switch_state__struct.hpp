// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/JacksSwitchState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/jacks_switch_state.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__JACKS_SWITCH_STATE__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__JACKS_SWITCH_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__JacksSwitchState __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__JacksSwitchState __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct JacksSwitchState_
{
  using Type = JacksSwitchState_<ContainerAllocator>;

  explicit JacksSwitchState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->left_retracted = false;
      this->right_retracted = false;
      this->rear_retracted = false;
      this->left_on_ground = false;
      this->right_on_ground = false;
      this->rear_on_ground = false;
    }
  }

  explicit JacksSwitchState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->left_retracted = false;
      this->right_retracted = false;
      this->rear_retracted = false;
      this->left_on_ground = false;
      this->right_on_ground = false;
      this->rear_on_ground = false;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _left_retracted_type =
    bool;
  _left_retracted_type left_retracted;
  using _right_retracted_type =
    bool;
  _right_retracted_type right_retracted;
  using _rear_retracted_type =
    bool;
  _rear_retracted_type rear_retracted;
  using _left_on_ground_type =
    bool;
  _left_on_ground_type left_on_ground;
  using _right_on_ground_type =
    bool;
  _right_on_ground_type right_on_ground;
  using _rear_on_ground_type =
    bool;
  _rear_on_ground_type rear_on_ground;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__left_retracted(
    const bool & _arg)
  {
    this->left_retracted = _arg;
    return *this;
  }
  Type & set__right_retracted(
    const bool & _arg)
  {
    this->right_retracted = _arg;
    return *this;
  }
  Type & set__rear_retracted(
    const bool & _arg)
  {
    this->rear_retracted = _arg;
    return *this;
  }
  Type & set__left_on_ground(
    const bool & _arg)
  {
    this->left_on_ground = _arg;
    return *this;
  }
  Type & set__right_on_ground(
    const bool & _arg)
  {
    this->right_on_ground = _arg;
    return *this;
  }
  Type & set__rear_on_ground(
    const bool & _arg)
  {
    this->rear_on_ground = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::JacksSwitchState_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::JacksSwitchState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::JacksSwitchState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::JacksSwitchState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__JacksSwitchState
    std::shared_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__JacksSwitchState
    std::shared_ptr<drill_msgs::msg::JacksSwitchState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JacksSwitchState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->left_retracted != other.left_retracted) {
      return false;
    }
    if (this->right_retracted != other.right_retracted) {
      return false;
    }
    if (this->rear_retracted != other.rear_retracted) {
      return false;
    }
    if (this->left_on_ground != other.left_on_ground) {
      return false;
    }
    if (this->right_on_ground != other.right_on_ground) {
      return false;
    }
    if (this->rear_on_ground != other.rear_on_ground) {
      return false;
    }
    return true;
  }
  bool operator!=(const JacksSwitchState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JacksSwitchState_

// alias to use template instance with default allocator
using JacksSwitchState =
  drill_msgs::msg::JacksSwitchState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__JACKS_SWITCH_STATE__STRUCT_HPP_
