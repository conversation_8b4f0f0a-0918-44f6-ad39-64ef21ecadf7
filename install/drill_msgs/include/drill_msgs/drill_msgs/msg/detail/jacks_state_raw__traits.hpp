// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/JacksStateRaw.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/jacks_state_raw.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/jacks_state_raw__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const JacksStateRaw & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: front_left_len
  {
    out << "front_left_len: ";
    rosidl_generator_traits::value_to_yaml(msg.front_left_len, out);
    out << ", ";
  }

  // member: front_right_len
  {
    out << "front_right_len: ";
    rosidl_generator_traits::value_to_yaml(msg.front_right_len, out);
    out << ", ";
  }

  // member: rear_left_len
  {
    out << "rear_left_len: ";
    rosidl_generator_traits::value_to_yaml(msg.rear_left_len, out);
    out << ", ";
  }

  // member: rear_right_len
  {
    out << "rear_right_len: ";
    rosidl_generator_traits::value_to_yaml(msg.rear_right_len, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JacksStateRaw & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: front_left_len
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "front_left_len: ";
    rosidl_generator_traits::value_to_yaml(msg.front_left_len, out);
    out << "\n";
  }

  // member: front_right_len
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "front_right_len: ";
    rosidl_generator_traits::value_to_yaml(msg.front_right_len, out);
    out << "\n";
  }

  // member: rear_left_len
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rear_left_len: ";
    rosidl_generator_traits::value_to_yaml(msg.rear_left_len, out);
    out << "\n";
  }

  // member: rear_right_len
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rear_right_len: ";
    rosidl_generator_traits::value_to_yaml(msg.rear_right_len, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JacksStateRaw & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::JacksStateRaw & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::JacksStateRaw & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::JacksStateRaw>()
{
  return "drill_msgs::msg::JacksStateRaw";
}

template<>
inline const char * name<drill_msgs::msg::JacksStateRaw>()
{
  return "drill_msgs/msg/JacksStateRaw";
}

template<>
struct has_fixed_size<drill_msgs::msg::JacksStateRaw>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<drill_msgs::msg::JacksStateRaw>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<drill_msgs::msg::JacksStateRaw>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__JACKS_STATE_RAW__TRAITS_HPP_
