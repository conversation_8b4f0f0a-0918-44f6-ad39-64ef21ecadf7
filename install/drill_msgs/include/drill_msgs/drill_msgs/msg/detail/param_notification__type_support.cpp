// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from drill_msgs:msg/ParamNotification.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "drill_msgs/msg/detail/param_notification__functions.h"
#include "drill_msgs/msg/detail/param_notification__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace drill_msgs
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void ParamNotification_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) drill_msgs::msg::ParamNotification(_init);
}

void ParamNotification_fini_function(void * message_memory)
{
  auto typed_message = static_cast<drill_msgs::msg::ParamNotification *>(message_memory);
  typed_message->~ParamNotification();
}

size_t size_function__ParamNotification__keys(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<std_msgs::msg::String> *>(untyped_member);
  return member->size();
}

const void * get_const_function__ParamNotification__keys(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<std_msgs::msg::String> *>(untyped_member);
  return &member[index];
}

void * get_function__ParamNotification__keys(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<std_msgs::msg::String> *>(untyped_member);
  return &member[index];
}

void fetch_function__ParamNotification__keys(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const std_msgs::msg::String *>(
    get_const_function__ParamNotification__keys(untyped_member, index));
  auto & value = *reinterpret_cast<std_msgs::msg::String *>(untyped_value);
  value = item;
}

void assign_function__ParamNotification__keys(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<std_msgs::msg::String *>(
    get_function__ParamNotification__keys(untyped_member, index));
  const auto & value = *reinterpret_cast<const std_msgs::msg::String *>(untyped_value);
  item = value;
}

void resize_function__ParamNotification__keys(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<std_msgs::msg::String> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember ParamNotification_message_member_array[2] = {
  {
    "header",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<std_msgs::msg::Header>(),  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::msg::ParamNotification, header),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "keys",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<std_msgs::msg::String>(),  // members of sub message
    false,  // is key
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::msg::ParamNotification, keys),  // bytes offset in struct
    nullptr,  // default value
    size_function__ParamNotification__keys,  // size() function pointer
    get_const_function__ParamNotification__keys,  // get_const(index) function pointer
    get_function__ParamNotification__keys,  // get(index) function pointer
    fetch_function__ParamNotification__keys,  // fetch(index, &value) function pointer
    assign_function__ParamNotification__keys,  // assign(index, value) function pointer
    resize_function__ParamNotification__keys  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers ParamNotification_message_members = {
  "drill_msgs::msg",  // message namespace
  "ParamNotification",  // message name
  2,  // number of fields
  sizeof(drill_msgs::msg::ParamNotification),
  false,  // has_any_key_member_
  ParamNotification_message_member_array,  // message members
  ParamNotification_init_function,  // function to initialize message memory (memory has to be allocated)
  ParamNotification_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t ParamNotification_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &ParamNotification_message_members,
  get_message_typesupport_handle_function,
  &drill_msgs__msg__ParamNotification__get_type_hash,
  &drill_msgs__msg__ParamNotification__get_type_description,
  &drill_msgs__msg__ParamNotification__get_type_description_sources,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace drill_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<drill_msgs::msg::ParamNotification>()
{
  return &::drill_msgs::msg::rosidl_typesupport_introspection_cpp::ParamNotification_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, drill_msgs, msg, ParamNotification)() {
  return &::drill_msgs::msg::rosidl_typesupport_introspection_cpp::ParamNotification_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
