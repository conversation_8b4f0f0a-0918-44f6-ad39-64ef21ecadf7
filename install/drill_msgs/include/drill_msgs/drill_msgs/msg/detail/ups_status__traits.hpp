// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from drill_msgs:msg/UpsStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/ups_status.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__UPS_STATUS__TRAITS_HPP_
#define DRILL_MSGS__MSG__DETAIL__UPS_STATUS__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "drill_msgs/msg/detail/ups_status__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace drill_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const UpsStatus & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: batt_current
  {
    out << "batt_current: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_current, out);
    out << ", ";
  }

  // member: grid_current
  {
    out << "grid_current: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_current, out);
    out << ", ";
  }

  // member: batt_voltage
  {
    out << "batt_voltage: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_voltage, out);
    out << ", ";
  }

  // member: grid_voltage
  {
    out << "grid_voltage: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_voltage, out);
    out << ", ";
  }

  // member: charge_current
  {
    out << "charge_current: ";
    rosidl_generator_traits::value_to_yaml(msg.charge_current, out);
    out << ", ";
  }

  // member: batt_active
  {
    out << "batt_active: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_active, out);
    out << ", ";
  }

  // member: grid_active
  {
    out << "grid_active: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_active, out);
    out << ", ";
  }

  // member: batt_voltage_ok
  {
    out << "batt_voltage_ok: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_voltage_ok, out);
    out << ", ";
  }

  // member: grid_voltage_ok
  {
    out << "grid_voltage_ok: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_voltage_ok, out);
    out << ", ";
  }

  // member: batt_current_exceeded
  {
    out << "batt_current_exceeded: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_current_exceeded, out);
    out << ", ";
  }

  // member: grid_current_exceeded
  {
    out << "grid_current_exceeded: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_current_exceeded, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const UpsStatus & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: batt_current
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batt_current: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_current, out);
    out << "\n";
  }

  // member: grid_current
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "grid_current: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_current, out);
    out << "\n";
  }

  // member: batt_voltage
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batt_voltage: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_voltage, out);
    out << "\n";
  }

  // member: grid_voltage
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "grid_voltage: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_voltage, out);
    out << "\n";
  }

  // member: charge_current
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "charge_current: ";
    rosidl_generator_traits::value_to_yaml(msg.charge_current, out);
    out << "\n";
  }

  // member: batt_active
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batt_active: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_active, out);
    out << "\n";
  }

  // member: grid_active
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "grid_active: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_active, out);
    out << "\n";
  }

  // member: batt_voltage_ok
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batt_voltage_ok: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_voltage_ok, out);
    out << "\n";
  }

  // member: grid_voltage_ok
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "grid_voltage_ok: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_voltage_ok, out);
    out << "\n";
  }

  // member: batt_current_exceeded
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batt_current_exceeded: ";
    rosidl_generator_traits::value_to_yaml(msg.batt_current_exceeded, out);
    out << "\n";
  }

  // member: grid_current_exceeded
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "grid_current_exceeded: ";
    rosidl_generator_traits::value_to_yaml(msg.grid_current_exceeded, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const UpsStatus & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace drill_msgs

namespace rosidl_generator_traits
{

[[deprecated("use drill_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const drill_msgs::msg::UpsStatus & msg,
  std::ostream & out, size_t indentation = 0)
{
  drill_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use drill_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const drill_msgs::msg::UpsStatus & msg)
{
  return drill_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<drill_msgs::msg::UpsStatus>()
{
  return "drill_msgs::msg::UpsStatus";
}

template<>
inline const char * name<drill_msgs::msg::UpsStatus>()
{
  return "drill_msgs/msg/UpsStatus";
}

template<>
struct has_fixed_size<drill_msgs::msg::UpsStatus>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<drill_msgs::msg::UpsStatus>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<drill_msgs::msg::UpsStatus>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DRILL_MSGS__MSG__DETAIL__UPS_STATUS__TRAITS_HPP_
