// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from drill_msgs:msg/TowerCtrl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/tower_ctrl.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__TOWER_CTRL__STRUCT_HPP_
#define DRILL_MSGS__MSG__DETAIL__TOWER_CTRL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__drill_msgs__msg__TowerCtrl __attribute__((deprecated))
#else
# define DEPRECATED__drill_msgs__msg__TowerCtrl __declspec(deprecated)
#endif

namespace drill_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct TowerCtrl_
{
  using Type = TowerCtrl_<ContainerAllocator>;

  explicit TowerCtrl_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->tilt = 0.0f;
      this->vert_pins = 0l;
      this->incl_pins = 0l;
    }
  }

  explicit TowerCtrl_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->tilt = 0.0f;
      this->vert_pins = 0l;
      this->incl_pins = 0l;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _tilt_type =
    float;
  _tilt_type tilt;
  using _vert_pins_type =
    int32_t;
  _vert_pins_type vert_pins;
  using _incl_pins_type =
    int32_t;
  _incl_pins_type incl_pins;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__tilt(
    const float & _arg)
  {
    this->tilt = _arg;
    return *this;
  }
  Type & set__vert_pins(
    const int32_t & _arg)
  {
    this->vert_pins = _arg;
    return *this;
  }
  Type & set__incl_pins(
    const int32_t & _arg)
  {
    this->incl_pins = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    drill_msgs::msg::TowerCtrl_<ContainerAllocator> *;
  using ConstRawPtr =
    const drill_msgs::msg::TowerCtrl_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::TowerCtrl_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      drill_msgs::msg::TowerCtrl_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__drill_msgs__msg__TowerCtrl
    std::shared_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__drill_msgs__msg__TowerCtrl
    std::shared_ptr<drill_msgs::msg::TowerCtrl_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const TowerCtrl_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->tilt != other.tilt) {
      return false;
    }
    if (this->vert_pins != other.vert_pins) {
      return false;
    }
    if (this->incl_pins != other.incl_pins) {
      return false;
    }
    return true;
  }
  bool operator!=(const TowerCtrl_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct TowerCtrl_

// alias to use template instance with default allocator
using TowerCtrl =
  drill_msgs::msg::TowerCtrl_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__TOWER_CTRL__STRUCT_HPP_
