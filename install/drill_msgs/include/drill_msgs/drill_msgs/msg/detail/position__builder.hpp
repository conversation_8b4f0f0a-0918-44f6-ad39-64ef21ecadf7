// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from drill_msgs:msg/Position.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "drill_msgs/msg/position.hpp"


#ifndef DRILL_MSGS__MSG__DETAIL__POSITION__BUILDER_HPP_
#define DRILL_MSGS__MSG__DETAIL__POSITION__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "drill_msgs/msg/detail/position__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace drill_msgs
{

namespace msg
{

namespace builder
{

class Init_Position_is_reliable
{
public:
  explicit Init_Position_is_reliable(::drill_msgs::msg::Position & msg)
  : msg_(msg)
  {}
  ::drill_msgs::msg::Position is_reliable(::drill_msgs::msg::Position::_is_reliable_type arg)
  {
    msg_.is_reliable = std::move(arg);
    return std::move(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

class Init_Position_yaw
{
public:
  explicit Init_Position_yaw(::drill_msgs::msg::Position & msg)
  : msg_(msg)
  {}
  Init_Position_is_reliable yaw(::drill_msgs::msg::Position::_yaw_type arg)
  {
    msg_.yaw = std::move(arg);
    return Init_Position_is_reliable(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

class Init_Position_z
{
public:
  explicit Init_Position_z(::drill_msgs::msg::Position & msg)
  : msg_(msg)
  {}
  Init_Position_yaw z(::drill_msgs::msg::Position::_z_type arg)
  {
    msg_.z = std::move(arg);
    return Init_Position_yaw(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

class Init_Position_y
{
public:
  explicit Init_Position_y(::drill_msgs::msg::Position & msg)
  : msg_(msg)
  {}
  Init_Position_z y(::drill_msgs::msg::Position::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_Position_z(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

class Init_Position_x
{
public:
  explicit Init_Position_x(::drill_msgs::msg::Position & msg)
  : msg_(msg)
  {}
  Init_Position_y x(::drill_msgs::msg::Position::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_Position_y(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

class Init_Position_header
{
public:
  Init_Position_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Position_x header(::drill_msgs::msg::Position::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_Position_x(msg_);
  }

private:
  ::drill_msgs::msg::Position msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::drill_msgs::msg::Position>()
{
  return drill_msgs::msg::builder::Init_Position_header();
}

}  // namespace drill_msgs

#endif  // DRILL_MSGS__MSG__DETAIL__POSITION__BUILDER_HPP_
