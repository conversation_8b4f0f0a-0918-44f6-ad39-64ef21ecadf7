// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from drill_msgs:msg/StateMachineStatus.idl
// generated code does not contain a copyright notice

#ifndef DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__TYPE_SUPPORT_HPP_
#define DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "drill_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_drill_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  drill_msgs,
  msg,
  StateMachineStatus
)();
#ifdef __cplusplus
}
#endif

#endif  // DRILL_MSGS__MSG__DETAIL__STATE_MACHINE_STATUS__TYPE_SUPPORT_HPP_
