// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from drill_msgs:srv/GetCurrentDriveAction.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "drill_msgs/srv/detail/get_current_drive_action__functions.h"
#include "drill_msgs/srv/detail/get_current_drive_action__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace drill_msgs
{

namespace srv
{

namespace rosidl_typesupport_introspection_cpp
{

void GetCurrentDriveAction_Request_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) drill_msgs::srv::GetCurrentDriveAction_Request(_init);
}

void GetCurrentDriveAction_Request_fini_function(void * message_memory)
{
  auto typed_message = static_cast<drill_msgs::srv::GetCurrentDriveAction_Request *>(message_memory);
  typed_message->~GetCurrentDriveAction_Request();
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember GetCurrentDriveAction_Request_message_member_array[1] = {
  {
    "structure_needs_at_least_one_member",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Request, structure_needs_at_least_one_member),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers GetCurrentDriveAction_Request_message_members = {
  "drill_msgs::srv",  // message namespace
  "GetCurrentDriveAction_Request",  // message name
  1,  // number of fields
  sizeof(drill_msgs::srv::GetCurrentDriveAction_Request),
  false,  // has_any_key_member_
  GetCurrentDriveAction_Request_message_member_array,  // message members
  GetCurrentDriveAction_Request_init_function,  // function to initialize message memory (memory has to be allocated)
  GetCurrentDriveAction_Request_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t GetCurrentDriveAction_Request_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &GetCurrentDriveAction_Request_message_members,
  get_message_typesupport_handle_function,
  &drill_msgs__srv__GetCurrentDriveAction_Request__get_type_hash,
  &drill_msgs__srv__GetCurrentDriveAction_Request__get_type_description,
  &drill_msgs__srv__GetCurrentDriveAction_Request__get_type_description_sources,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace srv

}  // namespace drill_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Request>()
{
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Request_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, drill_msgs, srv, GetCurrentDriveAction_Request)() {
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Request_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif

// already included above
// #include "array"
// already included above
// #include "cstddef"
// already included above
// #include "string"
// already included above
// #include "vector"
// already included above
// #include "rosidl_runtime_c/message_type_support_struct.h"
// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"
// already included above
// #include "rosidl_typesupport_interface/macros.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__functions.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__struct.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/field_types.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/identifier.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace drill_msgs
{

namespace srv
{

namespace rosidl_typesupport_introspection_cpp
{

void GetCurrentDriveAction_Response_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) drill_msgs::srv::GetCurrentDriveAction_Response(_init);
}

void GetCurrentDriveAction_Response_fini_function(void * message_memory)
{
  auto typed_message = static_cast<drill_msgs::srv::GetCurrentDriveAction_Response *>(message_memory);
  typed_message->~GetCurrentDriveAction_Response();
}

size_t size_function__GetCurrentDriveAction_Response__path_segments(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<drill_msgs::msg::Path> *>(untyped_member);
  return member->size();
}

const void * get_const_function__GetCurrentDriveAction_Response__path_segments(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<drill_msgs::msg::Path> *>(untyped_member);
  return &member[index];
}

void * get_function__GetCurrentDriveAction_Response__path_segments(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<drill_msgs::msg::Path> *>(untyped_member);
  return &member[index];
}

void fetch_function__GetCurrentDriveAction_Response__path_segments(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const drill_msgs::msg::Path *>(
    get_const_function__GetCurrentDriveAction_Response__path_segments(untyped_member, index));
  auto & value = *reinterpret_cast<drill_msgs::msg::Path *>(untyped_value);
  value = item;
}

void assign_function__GetCurrentDriveAction_Response__path_segments(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<drill_msgs::msg::Path *>(
    get_function__GetCurrentDriveAction_Response__path_segments(untyped_member, index));
  const auto & value = *reinterpret_cast<const drill_msgs::msg::Path *>(untyped_value);
  item = value;
}

void resize_function__GetCurrentDriveAction_Response__path_segments(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<drill_msgs::msg::Path> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember GetCurrentDriveAction_Response_message_member_array[5] = {
  {
    "header",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<std_msgs::msg::Header>(),  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Response, header),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "action_id",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Response, action_id),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "path_segments",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::msg::Path>(),  // members of sub message
    false,  // is key
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Response, path_segments),  // bytes offset in struct
    nullptr,  // default value
    size_function__GetCurrentDriveAction_Response__path_segments,  // size() function pointer
    get_const_function__GetCurrentDriveAction_Response__path_segments,  // get_const(index) function pointer
    get_function__GetCurrentDriveAction_Response__path_segments,  // get(index) function pointer
    fetch_function__GetCurrentDriveAction_Response__path_segments,  // fetch(index, &value) function pointer
    assign_function__GetCurrentDriveAction_Response__path_segments,  // assign(index, value) function pointer
    resize_function__GetCurrentDriveAction_Response__path_segments  // resize(index) function pointer
  },
  {
    "success",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Response, success),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "message",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Response, message),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers GetCurrentDriveAction_Response_message_members = {
  "drill_msgs::srv",  // message namespace
  "GetCurrentDriveAction_Response",  // message name
  5,  // number of fields
  sizeof(drill_msgs::srv::GetCurrentDriveAction_Response),
  false,  // has_any_key_member_
  GetCurrentDriveAction_Response_message_member_array,  // message members
  GetCurrentDriveAction_Response_init_function,  // function to initialize message memory (memory has to be allocated)
  GetCurrentDriveAction_Response_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t GetCurrentDriveAction_Response_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &GetCurrentDriveAction_Response_message_members,
  get_message_typesupport_handle_function,
  &drill_msgs__srv__GetCurrentDriveAction_Response__get_type_hash,
  &drill_msgs__srv__GetCurrentDriveAction_Response__get_type_description,
  &drill_msgs__srv__GetCurrentDriveAction_Response__get_type_description_sources,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace srv

}  // namespace drill_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Response>()
{
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Response_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, drill_msgs, srv, GetCurrentDriveAction_Response)() {
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Response_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif

// already included above
// #include "array"
// already included above
// #include "cstddef"
// already included above
// #include "string"
// already included above
// #include "vector"
// already included above
// #include "rosidl_runtime_c/message_type_support_struct.h"
// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"
// already included above
// #include "rosidl_typesupport_interface/macros.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__functions.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__struct.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/field_types.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/identifier.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace drill_msgs
{

namespace srv
{

namespace rosidl_typesupport_introspection_cpp
{

void GetCurrentDriveAction_Event_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) drill_msgs::srv::GetCurrentDriveAction_Event(_init);
}

void GetCurrentDriveAction_Event_fini_function(void * message_memory)
{
  auto typed_message = static_cast<drill_msgs::srv::GetCurrentDriveAction_Event *>(message_memory);
  typed_message->~GetCurrentDriveAction_Event();
}

size_t size_function__GetCurrentDriveAction_Event__request(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<drill_msgs::srv::GetCurrentDriveAction_Request> *>(untyped_member);
  return member->size();
}

const void * get_const_function__GetCurrentDriveAction_Event__request(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<drill_msgs::srv::GetCurrentDriveAction_Request> *>(untyped_member);
  return &member[index];
}

void * get_function__GetCurrentDriveAction_Event__request(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<drill_msgs::srv::GetCurrentDriveAction_Request> *>(untyped_member);
  return &member[index];
}

void fetch_function__GetCurrentDriveAction_Event__request(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const drill_msgs::srv::GetCurrentDriveAction_Request *>(
    get_const_function__GetCurrentDriveAction_Event__request(untyped_member, index));
  auto & value = *reinterpret_cast<drill_msgs::srv::GetCurrentDriveAction_Request *>(untyped_value);
  value = item;
}

void assign_function__GetCurrentDriveAction_Event__request(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<drill_msgs::srv::GetCurrentDriveAction_Request *>(
    get_function__GetCurrentDriveAction_Event__request(untyped_member, index));
  const auto & value = *reinterpret_cast<const drill_msgs::srv::GetCurrentDriveAction_Request *>(untyped_value);
  item = value;
}

void resize_function__GetCurrentDriveAction_Event__request(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<drill_msgs::srv::GetCurrentDriveAction_Request> *>(untyped_member);
  member->resize(size);
}

size_t size_function__GetCurrentDriveAction_Event__response(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<drill_msgs::srv::GetCurrentDriveAction_Response> *>(untyped_member);
  return member->size();
}

const void * get_const_function__GetCurrentDriveAction_Event__response(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<drill_msgs::srv::GetCurrentDriveAction_Response> *>(untyped_member);
  return &member[index];
}

void * get_function__GetCurrentDriveAction_Event__response(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<drill_msgs::srv::GetCurrentDriveAction_Response> *>(untyped_member);
  return &member[index];
}

void fetch_function__GetCurrentDriveAction_Event__response(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const drill_msgs::srv::GetCurrentDriveAction_Response *>(
    get_const_function__GetCurrentDriveAction_Event__response(untyped_member, index));
  auto & value = *reinterpret_cast<drill_msgs::srv::GetCurrentDriveAction_Response *>(untyped_value);
  value = item;
}

void assign_function__GetCurrentDriveAction_Event__response(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<drill_msgs::srv::GetCurrentDriveAction_Response *>(
    get_function__GetCurrentDriveAction_Event__response(untyped_member, index));
  const auto & value = *reinterpret_cast<const drill_msgs::srv::GetCurrentDriveAction_Response *>(untyped_value);
  item = value;
}

void resize_function__GetCurrentDriveAction_Event__response(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<drill_msgs::srv::GetCurrentDriveAction_Response> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember GetCurrentDriveAction_Event_message_member_array[3] = {
  {
    "info",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<service_msgs::msg::ServiceEventInfo>(),  // members of sub message
    false,  // is key
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Event, info),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "request",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Request>(),  // members of sub message
    false,  // is key
    true,  // is array
    1,  // array size
    true,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Event, request),  // bytes offset in struct
    nullptr,  // default value
    size_function__GetCurrentDriveAction_Event__request,  // size() function pointer
    get_const_function__GetCurrentDriveAction_Event__request,  // get_const(index) function pointer
    get_function__GetCurrentDriveAction_Event__request,  // get(index) function pointer
    fetch_function__GetCurrentDriveAction_Event__request,  // fetch(index, &value) function pointer
    assign_function__GetCurrentDriveAction_Event__request,  // assign(index, value) function pointer
    resize_function__GetCurrentDriveAction_Event__request  // resize(index) function pointer
  },
  {
    "response",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Response>(),  // members of sub message
    false,  // is key
    true,  // is array
    1,  // array size
    true,  // is upper bound
    offsetof(drill_msgs::srv::GetCurrentDriveAction_Event, response),  // bytes offset in struct
    nullptr,  // default value
    size_function__GetCurrentDriveAction_Event__response,  // size() function pointer
    get_const_function__GetCurrentDriveAction_Event__response,  // get_const(index) function pointer
    get_function__GetCurrentDriveAction_Event__response,  // get(index) function pointer
    fetch_function__GetCurrentDriveAction_Event__response,  // fetch(index, &value) function pointer
    assign_function__GetCurrentDriveAction_Event__response,  // assign(index, value) function pointer
    resize_function__GetCurrentDriveAction_Event__response  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers GetCurrentDriveAction_Event_message_members = {
  "drill_msgs::srv",  // message namespace
  "GetCurrentDriveAction_Event",  // message name
  3,  // number of fields
  sizeof(drill_msgs::srv::GetCurrentDriveAction_Event),
  false,  // has_any_key_member_
  GetCurrentDriveAction_Event_message_member_array,  // message members
  GetCurrentDriveAction_Event_init_function,  // function to initialize message memory (memory has to be allocated)
  GetCurrentDriveAction_Event_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t GetCurrentDriveAction_Event_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &GetCurrentDriveAction_Event_message_members,
  get_message_typesupport_handle_function,
  &drill_msgs__srv__GetCurrentDriveAction_Event__get_type_hash,
  &drill_msgs__srv__GetCurrentDriveAction_Event__get_type_description,
  &drill_msgs__srv__GetCurrentDriveAction_Event__get_type_description_sources,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace srv

}  // namespace drill_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Event>()
{
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Event_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, drill_msgs, srv, GetCurrentDriveAction_Event)() {
  return &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_Event_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_cpp/service_type_support.hpp"
// already included above
// #include "rosidl_typesupport_interface/macros.h"
// already included above
// #include "rosidl_typesupport_introspection_cpp/visibility_control.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__functions.h"
// already included above
// #include "drill_msgs/srv/detail/get_current_drive_action__struct.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/identifier.hpp"
// already included above
// #include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/service_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/service_type_support_decl.hpp"

namespace drill_msgs
{

namespace srv
{

namespace rosidl_typesupport_introspection_cpp
{

// this is intentionally not const to allow initialization later to prevent an initialization race
static ::rosidl_typesupport_introspection_cpp::ServiceMembers GetCurrentDriveAction_service_members = {
  "drill_msgs::srv",  // service namespace
  "GetCurrentDriveAction",  // service name
  // the following fields are initialized below on first access
  // see get_service_type_support_handle<drill_msgs::srv::GetCurrentDriveAction>()
  nullptr,  // request message
  nullptr,  // response message
  nullptr,  // event message
};

static const rosidl_service_type_support_t GetCurrentDriveAction_service_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &GetCurrentDriveAction_service_members,
  get_service_typesupport_handle_function,
  ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Request>(),
  ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Response>(),
  ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<drill_msgs::srv::GetCurrentDriveAction_Event>(),
  &::rosidl_typesupport_cpp::service_create_event_message<drill_msgs::srv::GetCurrentDriveAction>,
  &::rosidl_typesupport_cpp::service_destroy_event_message<drill_msgs::srv::GetCurrentDriveAction>,
  &drill_msgs__srv__GetCurrentDriveAction__get_type_hash,
  &drill_msgs__srv__GetCurrentDriveAction__get_type_description,
  &drill_msgs__srv__GetCurrentDriveAction__get_type_description_sources,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace srv

}  // namespace drill_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_service_type_support_t *
get_service_type_support_handle<drill_msgs::srv::GetCurrentDriveAction>()
{
  // get a handle to the value to be returned
  auto service_type_support =
    &::drill_msgs::srv::rosidl_typesupport_introspection_cpp::GetCurrentDriveAction_service_type_support_handle;
  // get a non-const and properly typed version of the data void *
  auto service_members = const_cast<::rosidl_typesupport_introspection_cpp::ServiceMembers *>(
    static_cast<const ::rosidl_typesupport_introspection_cpp::ServiceMembers *>(
      service_type_support->data));
  // make sure all of the service_members are initialized
  // if they are not, initialize them
  if (
    service_members->request_members_ == nullptr ||
    service_members->response_members_ == nullptr ||
    service_members->event_members_ == nullptr)
  {
    // initialize the request_members_ with the static function from the external library
    service_members->request_members_ = static_cast<
      const ::rosidl_typesupport_introspection_cpp::MessageMembers *
      >(
      ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<
        ::drill_msgs::srv::GetCurrentDriveAction_Request
      >()->data
      );
    // initialize the response_members_ with the static function from the external library
    service_members->response_members_ = static_cast<
      const ::rosidl_typesupport_introspection_cpp::MessageMembers *
      >(
      ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<
        ::drill_msgs::srv::GetCurrentDriveAction_Response
      >()->data
      );
    // initialize the event_members_ with the static function from the external library
    service_members->event_members_ = static_cast<
      const ::rosidl_typesupport_introspection_cpp::MessageMembers *
      >(
      ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<
        ::drill_msgs::srv::GetCurrentDriveAction_Event
      >()->data
      );
  }
  // finally return the properly initialized service_type_support handle
  return service_type_support;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_service_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, drill_msgs, srv, GetCurrentDriveAction)() {
  return ::rosidl_typesupport_introspection_cpp::get_service_type_support_handle<drill_msgs::srv::GetCurrentDriveAction>();
}

#ifdef __cplusplus
}
#endif
