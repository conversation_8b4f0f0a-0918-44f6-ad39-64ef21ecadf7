// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from drill_msgs:msg/DrillStateRaw.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "drill_msgs/msg/detail/drill_state_raw__struct.h"
#include "drill_msgs/msg/detail/drill_state_raw__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool drill_msgs__msg__drill_state_raw__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[46];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("drill_msgs.msg._drill_state_raw.DrillStateRaw", full_classname_dest, 45) == 0);
  }
  drill_msgs__msg__DrillStateRaw * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // head_pos
    PyObject * field = PyObject_GetAttrString(_pymsg, "head_pos");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->head_pos = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // head_angular_pos
    PyObject * field = PyObject_GetAttrString(_pymsg, "head_angular_pos");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->head_angular_pos = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // drill_rpm
    PyObject * field = PyObject_GetAttrString(_pymsg, "drill_rpm");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->drill_rpm = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // feed_pressure
    PyObject * field = PyObject_GetAttrString(_pymsg, "feed_pressure");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->feed_pressure = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rot_pressure
    PyObject * field = PyObject_GetAttrString(_pymsg, "rot_pressure");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rot_pressure = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // air_pressure
    PyObject * field = PyObject_GetAttrString(_pymsg, "air_pressure");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->air_pressure = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * drill_msgs__msg__drill_state_raw__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of DrillStateRaw */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("drill_msgs.msg._drill_state_raw");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "DrillStateRaw");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  drill_msgs__msg__DrillStateRaw * ros_message = (drill_msgs__msg__DrillStateRaw *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // head_pos
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->head_pos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "head_pos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // head_angular_pos
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->head_angular_pos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "head_angular_pos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // drill_rpm
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->drill_rpm);
    {
      int rc = PyObject_SetAttrString(_pymessage, "drill_rpm", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // feed_pressure
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->feed_pressure);
    {
      int rc = PyObject_SetAttrString(_pymessage, "feed_pressure", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rot_pressure
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rot_pressure);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rot_pressure", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // air_pressure
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->air_pressure);
    {
      int rc = PyObject_SetAttrString(_pymessage, "air_pressure", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
