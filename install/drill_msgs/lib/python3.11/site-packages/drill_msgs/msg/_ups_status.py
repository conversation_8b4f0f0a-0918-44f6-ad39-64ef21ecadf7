# generated from rosidl_generator_py/resource/_idl.py.em
# with input from drill_msgs:msg/UpsStatus.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_UpsStatus(type):
    """Metaclass of message 'UpsStatus'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('drill_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'drill_msgs.msg.UpsStatus')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__ups_status
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__ups_status
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__ups_status
            cls._TYPE_SUPPORT = module.type_support_msg__msg__ups_status
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__ups_status

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class UpsStatus(metaclass=Metaclass_UpsStatus):
    """Message class 'UpsStatus'."""

    __slots__ = [
        '_header',
        '_batt_current',
        '_grid_current',
        '_batt_voltage',
        '_grid_voltage',
        '_charge_current',
        '_batt_active',
        '_grid_active',
        '_batt_voltage_ok',
        '_grid_voltage_ok',
        '_batt_current_exceeded',
        '_grid_current_exceeded',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'batt_current': 'float',
        'grid_current': 'float',
        'batt_voltage': 'float',
        'grid_voltage': 'float',
        'charge_current': 'float',
        'batt_active': 'boolean',
        'grid_active': 'boolean',
        'batt_voltage_ok': 'boolean',
        'grid_voltage_ok': 'boolean',
        'batt_current_exceeded': 'boolean',
        'grid_current_exceeded': 'boolean',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.batt_current = kwargs.get('batt_current', float())
        self.grid_current = kwargs.get('grid_current', float())
        self.batt_voltage = kwargs.get('batt_voltage', float())
        self.grid_voltage = kwargs.get('grid_voltage', float())
        self.charge_current = kwargs.get('charge_current', float())
        self.batt_active = kwargs.get('batt_active', bool())
        self.grid_active = kwargs.get('grid_active', bool())
        self.batt_voltage_ok = kwargs.get('batt_voltage_ok', bool())
        self.grid_voltage_ok = kwargs.get('grid_voltage_ok', bool())
        self.batt_current_exceeded = kwargs.get('batt_current_exceeded', bool())
        self.grid_current_exceeded = kwargs.get('grid_current_exceeded', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.batt_current != other.batt_current:
            return False
        if self.grid_current != other.grid_current:
            return False
        if self.batt_voltage != other.batt_voltage:
            return False
        if self.grid_voltage != other.grid_voltage:
            return False
        if self.charge_current != other.charge_current:
            return False
        if self.batt_active != other.batt_active:
            return False
        if self.grid_active != other.grid_active:
            return False
        if self.batt_voltage_ok != other.batt_voltage_ok:
            return False
        if self.grid_voltage_ok != other.grid_voltage_ok:
            return False
        if self.batt_current_exceeded != other.batt_current_exceeded:
            return False
        if self.grid_current_exceeded != other.grid_current_exceeded:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def batt_current(self):
        """Message field 'batt_current'."""
        return self._batt_current

    @batt_current.setter
    def batt_current(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'batt_current' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'batt_current' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._batt_current = value

    @builtins.property
    def grid_current(self):
        """Message field 'grid_current'."""
        return self._grid_current

    @grid_current.setter
    def grid_current(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'grid_current' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'grid_current' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._grid_current = value

    @builtins.property
    def batt_voltage(self):
        """Message field 'batt_voltage'."""
        return self._batt_voltage

    @batt_voltage.setter
    def batt_voltage(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'batt_voltage' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'batt_voltage' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._batt_voltage = value

    @builtins.property
    def grid_voltage(self):
        """Message field 'grid_voltage'."""
        return self._grid_voltage

    @grid_voltage.setter
    def grid_voltage(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'grid_voltage' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'grid_voltage' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._grid_voltage = value

    @builtins.property
    def charge_current(self):
        """Message field 'charge_current'."""
        return self._charge_current

    @charge_current.setter
    def charge_current(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'charge_current' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'charge_current' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._charge_current = value

    @builtins.property
    def batt_active(self):
        """Message field 'batt_active'."""
        return self._batt_active

    @batt_active.setter
    def batt_active(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'batt_active' field must be of type 'bool'"
        self._batt_active = value

    @builtins.property
    def grid_active(self):
        """Message field 'grid_active'."""
        return self._grid_active

    @grid_active.setter
    def grid_active(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'grid_active' field must be of type 'bool'"
        self._grid_active = value

    @builtins.property
    def batt_voltage_ok(self):
        """Message field 'batt_voltage_ok'."""
        return self._batt_voltage_ok

    @batt_voltage_ok.setter
    def batt_voltage_ok(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'batt_voltage_ok' field must be of type 'bool'"
        self._batt_voltage_ok = value

    @builtins.property
    def grid_voltage_ok(self):
        """Message field 'grid_voltage_ok'."""
        return self._grid_voltage_ok

    @grid_voltage_ok.setter
    def grid_voltage_ok(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'grid_voltage_ok' field must be of type 'bool'"
        self._grid_voltage_ok = value

    @builtins.property
    def batt_current_exceeded(self):
        """Message field 'batt_current_exceeded'."""
        return self._batt_current_exceeded

    @batt_current_exceeded.setter
    def batt_current_exceeded(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'batt_current_exceeded' field must be of type 'bool'"
        self._batt_current_exceeded = value

    @builtins.property
    def grid_current_exceeded(self):
        """Message field 'grid_current_exceeded'."""
        return self._grid_current_exceeded

    @grid_current_exceeded.setter
    def grid_current_exceeded(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'grid_current_exceeded' field must be of type 'bool'"
        self._grid_current_exceeded = value
