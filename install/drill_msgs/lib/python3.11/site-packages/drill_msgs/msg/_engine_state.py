# generated from rosidl_generator_py/resource/_idl.py.em
# with input from drill_msgs:msg/EngineState.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_EngineState(type):
    """Metaclass of message 'EngineState'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('drill_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'drill_msgs.msg.EngineState')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__engine_state
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__engine_state
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__engine_state
            cls._TYPE_SUPPORT = module.type_support_msg__msg__engine_state
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__engine_state

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class EngineState(metaclass=Metaclass_EngineState):
    """Message class 'EngineState'."""

    __slots__ = [
        '_header',
        '_accel_pedal',
        '_engine_load',
        '_demand_torque',
        '_actual_torque',
        '_engine_speed',
        '_nominal_friction',
        '_desired_engine_speed',
        '_engine_hours',
        '_engine_total_fuel_used',
        '_engine_fuel_rate',
        '_battery_potential',
        '_keyswitch_potential',
        '_coolant_temperature',
        '_engine_fuel_temperature',
        '_engine_fuel_delivery_pressure',
        '_engine_oil_pressure',
        '_engine_coolant_level',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'accel_pedal': 'float',
        'engine_load': 'float',
        'demand_torque': 'float',
        'actual_torque': 'float',
        'engine_speed': 'float',
        'nominal_friction': 'float',
        'desired_engine_speed': 'float',
        'engine_hours': 'float',
        'engine_total_fuel_used': 'float',
        'engine_fuel_rate': 'float',
        'battery_potential': 'float',
        'keyswitch_potential': 'float',
        'coolant_temperature': 'float',
        'engine_fuel_temperature': 'float',
        'engine_fuel_delivery_pressure': 'float',
        'engine_oil_pressure': 'float',
        'engine_coolant_level': 'float',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.accel_pedal = kwargs.get('accel_pedal', float())
        self.engine_load = kwargs.get('engine_load', float())
        self.demand_torque = kwargs.get('demand_torque', float())
        self.actual_torque = kwargs.get('actual_torque', float())
        self.engine_speed = kwargs.get('engine_speed', float())
        self.nominal_friction = kwargs.get('nominal_friction', float())
        self.desired_engine_speed = kwargs.get('desired_engine_speed', float())
        self.engine_hours = kwargs.get('engine_hours', float())
        self.engine_total_fuel_used = kwargs.get('engine_total_fuel_used', float())
        self.engine_fuel_rate = kwargs.get('engine_fuel_rate', float())
        self.battery_potential = kwargs.get('battery_potential', float())
        self.keyswitch_potential = kwargs.get('keyswitch_potential', float())
        self.coolant_temperature = kwargs.get('coolant_temperature', float())
        self.engine_fuel_temperature = kwargs.get('engine_fuel_temperature', float())
        self.engine_fuel_delivery_pressure = kwargs.get('engine_fuel_delivery_pressure', float())
        self.engine_oil_pressure = kwargs.get('engine_oil_pressure', float())
        self.engine_coolant_level = kwargs.get('engine_coolant_level', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.accel_pedal != other.accel_pedal:
            return False
        if self.engine_load != other.engine_load:
            return False
        if self.demand_torque != other.demand_torque:
            return False
        if self.actual_torque != other.actual_torque:
            return False
        if self.engine_speed != other.engine_speed:
            return False
        if self.nominal_friction != other.nominal_friction:
            return False
        if self.desired_engine_speed != other.desired_engine_speed:
            return False
        if self.engine_hours != other.engine_hours:
            return False
        if self.engine_total_fuel_used != other.engine_total_fuel_used:
            return False
        if self.engine_fuel_rate != other.engine_fuel_rate:
            return False
        if self.battery_potential != other.battery_potential:
            return False
        if self.keyswitch_potential != other.keyswitch_potential:
            return False
        if self.coolant_temperature != other.coolant_temperature:
            return False
        if self.engine_fuel_temperature != other.engine_fuel_temperature:
            return False
        if self.engine_fuel_delivery_pressure != other.engine_fuel_delivery_pressure:
            return False
        if self.engine_oil_pressure != other.engine_oil_pressure:
            return False
        if self.engine_coolant_level != other.engine_coolant_level:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def accel_pedal(self):
        """Message field 'accel_pedal'."""
        return self._accel_pedal

    @accel_pedal.setter
    def accel_pedal(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'accel_pedal' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'accel_pedal' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._accel_pedal = value

    @builtins.property
    def engine_load(self):
        """Message field 'engine_load'."""
        return self._engine_load

    @engine_load.setter
    def engine_load(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_load' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_load' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_load = value

    @builtins.property
    def demand_torque(self):
        """Message field 'demand_torque'."""
        return self._demand_torque

    @demand_torque.setter
    def demand_torque(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'demand_torque' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'demand_torque' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._demand_torque = value

    @builtins.property
    def actual_torque(self):
        """Message field 'actual_torque'."""
        return self._actual_torque

    @actual_torque.setter
    def actual_torque(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'actual_torque' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'actual_torque' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._actual_torque = value

    @builtins.property
    def engine_speed(self):
        """Message field 'engine_speed'."""
        return self._engine_speed

    @engine_speed.setter
    def engine_speed(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_speed = value

    @builtins.property
    def nominal_friction(self):
        """Message field 'nominal_friction'."""
        return self._nominal_friction

    @nominal_friction.setter
    def nominal_friction(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'nominal_friction' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'nominal_friction' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._nominal_friction = value

    @builtins.property
    def desired_engine_speed(self):
        """Message field 'desired_engine_speed'."""
        return self._desired_engine_speed

    @desired_engine_speed.setter
    def desired_engine_speed(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'desired_engine_speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'desired_engine_speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._desired_engine_speed = value

    @builtins.property
    def engine_hours(self):
        """Message field 'engine_hours'."""
        return self._engine_hours

    @engine_hours.setter
    def engine_hours(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_hours' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_hours' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_hours = value

    @builtins.property
    def engine_total_fuel_used(self):
        """Message field 'engine_total_fuel_used'."""
        return self._engine_total_fuel_used

    @engine_total_fuel_used.setter
    def engine_total_fuel_used(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_total_fuel_used' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_total_fuel_used' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_total_fuel_used = value

    @builtins.property
    def engine_fuel_rate(self):
        """Message field 'engine_fuel_rate'."""
        return self._engine_fuel_rate

    @engine_fuel_rate.setter
    def engine_fuel_rate(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_fuel_rate' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_fuel_rate' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_fuel_rate = value

    @builtins.property
    def battery_potential(self):
        """Message field 'battery_potential'."""
        return self._battery_potential

    @battery_potential.setter
    def battery_potential(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'battery_potential' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'battery_potential' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._battery_potential = value

    @builtins.property
    def keyswitch_potential(self):
        """Message field 'keyswitch_potential'."""
        return self._keyswitch_potential

    @keyswitch_potential.setter
    def keyswitch_potential(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'keyswitch_potential' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'keyswitch_potential' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._keyswitch_potential = value

    @builtins.property
    def coolant_temperature(self):
        """Message field 'coolant_temperature'."""
        return self._coolant_temperature

    @coolant_temperature.setter
    def coolant_temperature(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'coolant_temperature' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'coolant_temperature' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._coolant_temperature = value

    @builtins.property
    def engine_fuel_temperature(self):
        """Message field 'engine_fuel_temperature'."""
        return self._engine_fuel_temperature

    @engine_fuel_temperature.setter
    def engine_fuel_temperature(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_fuel_temperature' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_fuel_temperature' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_fuel_temperature = value

    @builtins.property
    def engine_fuel_delivery_pressure(self):
        """Message field 'engine_fuel_delivery_pressure'."""
        return self._engine_fuel_delivery_pressure

    @engine_fuel_delivery_pressure.setter
    def engine_fuel_delivery_pressure(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_fuel_delivery_pressure' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_fuel_delivery_pressure' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_fuel_delivery_pressure = value

    @builtins.property
    def engine_oil_pressure(self):
        """Message field 'engine_oil_pressure'."""
        return self._engine_oil_pressure

    @engine_oil_pressure.setter
    def engine_oil_pressure(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_oil_pressure' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_oil_pressure' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_oil_pressure = value

    @builtins.property
    def engine_coolant_level(self):
        """Message field 'engine_coolant_level'."""
        return self._engine_coolant_level

    @engine_coolant_level.setter
    def engine_coolant_level(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'engine_coolant_level' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'engine_coolant_level' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._engine_coolant_level = value
