// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from drill_msgs:msg/DriveStatus.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "drill_msgs/msg/detail/drive_status__struct.h"
#include "drill_msgs/msg/detail/drive_status__functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool drill_msgs__msg__drive_status__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[41];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("drill_msgs.msg._drive_status.DriveStatus", full_classname_dest, 40) == 0);
  }
  drill_msgs__msg__DriveStatus * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // cur_action_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "cur_action_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->cur_action_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // last_action_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "last_action_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->last_action_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // cur_segment_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "cur_segment_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->cur_segment_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // cur_point_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "cur_point_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->cur_point_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // status
    PyObject * field = PyObject_GetAttrString(_pymsg, "status");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->status, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * drill_msgs__msg__drive_status__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of DriveStatus */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("drill_msgs.msg._drive_status");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "DriveStatus");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  drill_msgs__msg__DriveStatus * ros_message = (drill_msgs__msg__DriveStatus *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // cur_action_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->cur_action_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "cur_action_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // last_action_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->last_action_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "last_action_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // cur_segment_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->cur_segment_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "cur_segment_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // cur_point_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->cur_point_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "cur_point_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // status
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->status.data,
      strlen(ros_message->status.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
