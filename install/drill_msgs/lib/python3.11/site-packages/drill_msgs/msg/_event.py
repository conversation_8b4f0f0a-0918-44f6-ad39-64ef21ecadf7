# generated from rosidl_generator_py/resource/_idl.py.em
# with input from drill_msgs:msg/Event.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Event(type):
    """Metaclass of message 'Event'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
        'LEVEL_DEBUG': 0,
        'LEVEL_INFO': 1,
        'LEVEL_WARN': 2,
        'LEVEL_ERROR': 3,
        'LEVEL_CRITICAL': 4,
        'LEVEL_FATAL': 5,
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('drill_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'drill_msgs.msg.Event')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__event
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__event
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__event
            cls._TYPE_SUPPORT = module.type_support_msg__msg__event
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__event

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
            'LEVEL_DEBUG': cls.__constants['LEVEL_DEBUG'],
            'LEVEL_INFO': cls.__constants['LEVEL_INFO'],
            'LEVEL_WARN': cls.__constants['LEVEL_WARN'],
            'LEVEL_ERROR': cls.__constants['LEVEL_ERROR'],
            'LEVEL_CRITICAL': cls.__constants['LEVEL_CRITICAL'],
            'LEVEL_FATAL': cls.__constants['LEVEL_FATAL'],
        }

    @property
    def LEVEL_DEBUG(self):
        """Message constant 'LEVEL_DEBUG'."""
        return Metaclass_Event.__constants['LEVEL_DEBUG']

    @property
    def LEVEL_INFO(self):
        """Message constant 'LEVEL_INFO'."""
        return Metaclass_Event.__constants['LEVEL_INFO']

    @property
    def LEVEL_WARN(self):
        """Message constant 'LEVEL_WARN'."""
        return Metaclass_Event.__constants['LEVEL_WARN']

    @property
    def LEVEL_ERROR(self):
        """Message constant 'LEVEL_ERROR'."""
        return Metaclass_Event.__constants['LEVEL_ERROR']

    @property
    def LEVEL_CRITICAL(self):
        """Message constant 'LEVEL_CRITICAL'."""
        return Metaclass_Event.__constants['LEVEL_CRITICAL']

    @property
    def LEVEL_FATAL(self):
        """Message constant 'LEVEL_FATAL'."""
        return Metaclass_Event.__constants['LEVEL_FATAL']


class Event(metaclass=Metaclass_Event):
    """
    Message class 'Event'.

    Constants:
      LEVEL_DEBUG
      LEVEL_INFO
      LEVEL_WARN
      LEVEL_ERROR
      LEVEL_CRITICAL
      LEVEL_FATAL
    """

    __slots__ = [
        '_header',
        '_event_code',
        '_message',
        '_level',
        '_source',
        '_require_remote',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'event_code': 'string',
        'message': 'string',
        'level': 'uint8',
        'source': 'string',
        'require_remote': 'boolean',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.event_code = kwargs.get('event_code', str())
        self.message = kwargs.get('message', str())
        self.level = kwargs.get('level', int())
        self.source = kwargs.get('source', str())
        self.require_remote = kwargs.get('require_remote', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.event_code != other.event_code:
            return False
        if self.message != other.message:
            return False
        if self.level != other.level:
            return False
        if self.source != other.source:
            return False
        if self.require_remote != other.require_remote:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def event_code(self):
        """Message field 'event_code'."""
        return self._event_code

    @event_code.setter
    def event_code(self, value):
        if self._check_fields:
            assert \
                isinstance(value, str), \
                "The 'event_code' field must be of type 'str'"
        self._event_code = value

    @builtins.property
    def message(self):
        """Message field 'message'."""
        return self._message

    @message.setter
    def message(self, value):
        if self._check_fields:
            assert \
                isinstance(value, str), \
                "The 'message' field must be of type 'str'"
        self._message = value

    @builtins.property
    def level(self):
        """Message field 'level'."""
        return self._level

    @level.setter
    def level(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'level' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'level' field must be an unsigned integer in [0, 255]"
        self._level = value

    @builtins.property
    def source(self):
        """Message field 'source'."""
        return self._source

    @source.setter
    def source(self, value):
        if self._check_fields:
            assert \
                isinstance(value, str), \
                "The 'source' field must be of type 'str'"
        self._source = value

    @builtins.property
    def require_remote(self):
        """Message field 'require_remote'."""
        return self._require_remote

    @require_remote.setter
    def require_remote(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'require_remote' field must be of type 'bool'"
        self._require_remote = value
