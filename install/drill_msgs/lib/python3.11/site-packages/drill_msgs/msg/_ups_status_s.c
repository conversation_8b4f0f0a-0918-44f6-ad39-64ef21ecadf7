// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from drill_msgs:msg/UpsStatus.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "drill_msgs/msg/detail/ups_status__struct.h"
#include "drill_msgs/msg/detail/ups_status__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool drill_msgs__msg__ups_status__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[37];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("drill_msgs.msg._ups_status.UpsStatus", full_classname_dest, 36) == 0);
  }
  drill_msgs__msg__UpsStatus * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // batt_current
    PyObject * field = PyObject_GetAttrString(_pymsg, "batt_current");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->batt_current = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // grid_current
    PyObject * field = PyObject_GetAttrString(_pymsg, "grid_current");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->grid_current = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // batt_voltage
    PyObject * field = PyObject_GetAttrString(_pymsg, "batt_voltage");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->batt_voltage = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // grid_voltage
    PyObject * field = PyObject_GetAttrString(_pymsg, "grid_voltage");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->grid_voltage = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // charge_current
    PyObject * field = PyObject_GetAttrString(_pymsg, "charge_current");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->charge_current = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // batt_active
    PyObject * field = PyObject_GetAttrString(_pymsg, "batt_active");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->batt_active = (Py_True == field);
    Py_DECREF(field);
  }
  {  // grid_active
    PyObject * field = PyObject_GetAttrString(_pymsg, "grid_active");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->grid_active = (Py_True == field);
    Py_DECREF(field);
  }
  {  // batt_voltage_ok
    PyObject * field = PyObject_GetAttrString(_pymsg, "batt_voltage_ok");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->batt_voltage_ok = (Py_True == field);
    Py_DECREF(field);
  }
  {  // grid_voltage_ok
    PyObject * field = PyObject_GetAttrString(_pymsg, "grid_voltage_ok");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->grid_voltage_ok = (Py_True == field);
    Py_DECREF(field);
  }
  {  // batt_current_exceeded
    PyObject * field = PyObject_GetAttrString(_pymsg, "batt_current_exceeded");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->batt_current_exceeded = (Py_True == field);
    Py_DECREF(field);
  }
  {  // grid_current_exceeded
    PyObject * field = PyObject_GetAttrString(_pymsg, "grid_current_exceeded");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->grid_current_exceeded = (Py_True == field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * drill_msgs__msg__ups_status__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of UpsStatus */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("drill_msgs.msg._ups_status");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "UpsStatus");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  drill_msgs__msg__UpsStatus * ros_message = (drill_msgs__msg__UpsStatus *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batt_current
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->batt_current);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batt_current", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // grid_current
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->grid_current);
    {
      int rc = PyObject_SetAttrString(_pymessage, "grid_current", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batt_voltage
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->batt_voltage);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batt_voltage", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // grid_voltage
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->grid_voltage);
    {
      int rc = PyObject_SetAttrString(_pymessage, "grid_voltage", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // charge_current
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->charge_current);
    {
      int rc = PyObject_SetAttrString(_pymessage, "charge_current", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batt_active
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->batt_active ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batt_active", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // grid_active
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->grid_active ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "grid_active", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batt_voltage_ok
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->batt_voltage_ok ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batt_voltage_ok", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // grid_voltage_ok
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->grid_voltage_ok ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "grid_voltage_ok", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batt_current_exceeded
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->batt_current_exceeded ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batt_current_exceeded", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // grid_current_exceeded
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->grid_current_exceeded ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "grid_current_exceeded", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
