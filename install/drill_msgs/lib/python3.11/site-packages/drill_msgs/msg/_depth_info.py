# generated from rosidl_generator_py/resource/_idl.py.em
# with input from drill_msgs:msg/DepthInfo.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_DepthInfo(type):
    """Metaclass of message 'DepthInfo'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('drill_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'drill_msgs.msg.DepthInfo')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__depth_info
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__depth_info
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__depth_info
            cls._TYPE_SUPPORT = module.type_support_msg__msg__depth_info
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__depth_info

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class DepthInfo(metaclass=Metaclass_DepthInfo):
    """Message class 'DepthInfo'."""

    __slots__ = [
        '_header',
        '_hole_depth',
        '_current_bit_depth',
        '_tvd_hole_depth',
        '_head_pos',
        '_ground_head_pos',
        '_wellhead_altitude',
        '_hole_inclination',
        '_target_hole_depth',
        '_wellhead_x',
        '_wellhead_y',
        '_tracking_active',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'hole_depth': 'float',
        'current_bit_depth': 'float',
        'tvd_hole_depth': 'float',
        'head_pos': 'float',
        'ground_head_pos': 'float',
        'wellhead_altitude': 'float',
        'hole_inclination': 'float',
        'target_hole_depth': 'float',
        'wellhead_x': 'float',
        'wellhead_y': 'float',
        'tracking_active': 'boolean',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.hole_depth = kwargs.get('hole_depth', float())
        self.current_bit_depth = kwargs.get('current_bit_depth', float())
        self.tvd_hole_depth = kwargs.get('tvd_hole_depth', float())
        self.head_pos = kwargs.get('head_pos', float())
        self.ground_head_pos = kwargs.get('ground_head_pos', float())
        self.wellhead_altitude = kwargs.get('wellhead_altitude', float())
        self.hole_inclination = kwargs.get('hole_inclination', float())
        self.target_hole_depth = kwargs.get('target_hole_depth', float())
        self.wellhead_x = kwargs.get('wellhead_x', float())
        self.wellhead_y = kwargs.get('wellhead_y', float())
        self.tracking_active = kwargs.get('tracking_active', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.hole_depth != other.hole_depth:
            return False
        if self.current_bit_depth != other.current_bit_depth:
            return False
        if self.tvd_hole_depth != other.tvd_hole_depth:
            return False
        if self.head_pos != other.head_pos:
            return False
        if self.ground_head_pos != other.ground_head_pos:
            return False
        if self.wellhead_altitude != other.wellhead_altitude:
            return False
        if self.hole_inclination != other.hole_inclination:
            return False
        if self.target_hole_depth != other.target_hole_depth:
            return False
        if self.wellhead_x != other.wellhead_x:
            return False
        if self.wellhead_y != other.wellhead_y:
            return False
        if self.tracking_active != other.tracking_active:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def hole_depth(self):
        """Message field 'hole_depth'."""
        return self._hole_depth

    @hole_depth.setter
    def hole_depth(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'hole_depth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'hole_depth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._hole_depth = value

    @builtins.property
    def current_bit_depth(self):
        """Message field 'current_bit_depth'."""
        return self._current_bit_depth

    @current_bit_depth.setter
    def current_bit_depth(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'current_bit_depth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'current_bit_depth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._current_bit_depth = value

    @builtins.property
    def tvd_hole_depth(self):
        """Message field 'tvd_hole_depth'."""
        return self._tvd_hole_depth

    @tvd_hole_depth.setter
    def tvd_hole_depth(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'tvd_hole_depth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'tvd_hole_depth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._tvd_hole_depth = value

    @builtins.property
    def head_pos(self):
        """Message field 'head_pos'."""
        return self._head_pos

    @head_pos.setter
    def head_pos(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'head_pos' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'head_pos' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._head_pos = value

    @builtins.property
    def ground_head_pos(self):
        """Message field 'ground_head_pos'."""
        return self._ground_head_pos

    @ground_head_pos.setter
    def ground_head_pos(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'ground_head_pos' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'ground_head_pos' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._ground_head_pos = value

    @builtins.property
    def wellhead_altitude(self):
        """Message field 'wellhead_altitude'."""
        return self._wellhead_altitude

    @wellhead_altitude.setter
    def wellhead_altitude(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'wellhead_altitude' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'wellhead_altitude' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._wellhead_altitude = value

    @builtins.property
    def hole_inclination(self):
        """Message field 'hole_inclination'."""
        return self._hole_inclination

    @hole_inclination.setter
    def hole_inclination(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'hole_inclination' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'hole_inclination' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._hole_inclination = value

    @builtins.property
    def target_hole_depth(self):
        """Message field 'target_hole_depth'."""
        return self._target_hole_depth

    @target_hole_depth.setter
    def target_hole_depth(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'target_hole_depth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'target_hole_depth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._target_hole_depth = value

    @builtins.property
    def wellhead_x(self):
        """Message field 'wellhead_x'."""
        return self._wellhead_x

    @wellhead_x.setter
    def wellhead_x(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'wellhead_x' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'wellhead_x' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._wellhead_x = value

    @builtins.property
    def wellhead_y(self):
        """Message field 'wellhead_y'."""
        return self._wellhead_y

    @wellhead_y.setter
    def wellhead_y(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'wellhead_y' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'wellhead_y' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._wellhead_y = value

    @builtins.property
    def tracking_active(self):
        """Message field 'tracking_active'."""
        return self._tracking_active

    @tracking_active.setter
    def tracking_active(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'tracking_active' field must be of type 'bool'"
        self._tracking_active = value
