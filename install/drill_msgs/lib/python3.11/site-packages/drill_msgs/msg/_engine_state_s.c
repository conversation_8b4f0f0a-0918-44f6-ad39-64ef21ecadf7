// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from drill_msgs:msg/EngineState.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "drill_msgs/msg/detail/engine_state__struct.h"
#include "drill_msgs/msg/detail/engine_state__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool drill_msgs__msg__engine_state__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[41];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("drill_msgs.msg._engine_state.EngineState", full_classname_dest, 40) == 0);
  }
  drill_msgs__msg__EngineState * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // accel_pedal
    PyObject * field = PyObject_GetAttrString(_pymsg, "accel_pedal");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accel_pedal = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_load
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_load");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_load = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // demand_torque
    PyObject * field = PyObject_GetAttrString(_pymsg, "demand_torque");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->demand_torque = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // actual_torque
    PyObject * field = PyObject_GetAttrString(_pymsg, "actual_torque");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->actual_torque = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // nominal_friction
    PyObject * field = PyObject_GetAttrString(_pymsg, "nominal_friction");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->nominal_friction = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // desired_engine_speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "desired_engine_speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->desired_engine_speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_hours
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_hours");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_hours = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_total_fuel_used
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_total_fuel_used");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_total_fuel_used = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_fuel_rate
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_fuel_rate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_fuel_rate = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // battery_potential
    PyObject * field = PyObject_GetAttrString(_pymsg, "battery_potential");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->battery_potential = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // keyswitch_potential
    PyObject * field = PyObject_GetAttrString(_pymsg, "keyswitch_potential");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->keyswitch_potential = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // coolant_temperature
    PyObject * field = PyObject_GetAttrString(_pymsg, "coolant_temperature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->coolant_temperature = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_fuel_temperature
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_fuel_temperature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_fuel_temperature = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_fuel_delivery_pressure
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_fuel_delivery_pressure");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_fuel_delivery_pressure = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_oil_pressure
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_oil_pressure");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_oil_pressure = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // engine_coolant_level
    PyObject * field = PyObject_GetAttrString(_pymsg, "engine_coolant_level");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->engine_coolant_level = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * drill_msgs__msg__engine_state__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of EngineState */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("drill_msgs.msg._engine_state");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "EngineState");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  drill_msgs__msg__EngineState * ros_message = (drill_msgs__msg__EngineState *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accel_pedal
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accel_pedal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accel_pedal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_load
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_load);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_load", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // demand_torque
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->demand_torque);
    {
      int rc = PyObject_SetAttrString(_pymessage, "demand_torque", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // actual_torque
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->actual_torque);
    {
      int rc = PyObject_SetAttrString(_pymessage, "actual_torque", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // nominal_friction
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->nominal_friction);
    {
      int rc = PyObject_SetAttrString(_pymessage, "nominal_friction", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // desired_engine_speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->desired_engine_speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "desired_engine_speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_hours
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_hours);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_hours", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_total_fuel_used
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_total_fuel_used);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_total_fuel_used", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_fuel_rate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_fuel_rate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_fuel_rate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // battery_potential
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->battery_potential);
    {
      int rc = PyObject_SetAttrString(_pymessage, "battery_potential", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // keyswitch_potential
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->keyswitch_potential);
    {
      int rc = PyObject_SetAttrString(_pymessage, "keyswitch_potential", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // coolant_temperature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->coolant_temperature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "coolant_temperature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_fuel_temperature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_fuel_temperature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_fuel_temperature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_fuel_delivery_pressure
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_fuel_delivery_pressure);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_fuel_delivery_pressure", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_oil_pressure
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_oil_pressure);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_oil_pressure", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // engine_coolant_level
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->engine_coolant_level);
    {
      int rc = PyObject_SetAttrString(_pymessage, "engine_coolant_level", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
