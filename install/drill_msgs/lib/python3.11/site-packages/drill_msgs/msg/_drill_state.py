# generated from rosidl_generator_py/resource/_idl.py.em
# with input from drill_msgs:msg/DrillState.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_DrillState(type):
    """Metaclass of message 'DrillState'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('drill_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'drill_msgs.msg.DrillState')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__drill_state
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__drill_state
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__drill_state
            cls._TYPE_SUPPORT = module.type_support_msg__msg__drill_state
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__drill_state

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class DrillState(metaclass=Metaclass_DrillState):
    """Message class 'DrillState'."""

    __slots__ = [
        '_header',
        '_head_pos',
        '_head_speed',
        '_head_angular_pos',
        '_drill_rpm',
        '_feed_pressure',
        '_rot_pressure',
        '_air_pressure',
        '_head_pos_is_reliable',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'head_pos': 'float',
        'head_speed': 'float',
        'head_angular_pos': 'float',
        'drill_rpm': 'float',
        'feed_pressure': 'float',
        'rot_pressure': 'float',
        'air_pressure': 'float',
        'head_pos_is_reliable': 'boolean',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.head_pos = kwargs.get('head_pos', float())
        self.head_speed = kwargs.get('head_speed', float())
        self.head_angular_pos = kwargs.get('head_angular_pos', float())
        self.drill_rpm = kwargs.get('drill_rpm', float())
        self.feed_pressure = kwargs.get('feed_pressure', float())
        self.rot_pressure = kwargs.get('rot_pressure', float())
        self.air_pressure = kwargs.get('air_pressure', float())
        self.head_pos_is_reliable = kwargs.get('head_pos_is_reliable', bool())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.head_pos != other.head_pos:
            return False
        if self.head_speed != other.head_speed:
            return False
        if self.head_angular_pos != other.head_angular_pos:
            return False
        if self.drill_rpm != other.drill_rpm:
            return False
        if self.feed_pressure != other.feed_pressure:
            return False
        if self.rot_pressure != other.rot_pressure:
            return False
        if self.air_pressure != other.air_pressure:
            return False
        if self.head_pos_is_reliable != other.head_pos_is_reliable:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def head_pos(self):
        """Message field 'head_pos'."""
        return self._head_pos

    @head_pos.setter
    def head_pos(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'head_pos' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'head_pos' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._head_pos = value

    @builtins.property
    def head_speed(self):
        """Message field 'head_speed'."""
        return self._head_speed

    @head_speed.setter
    def head_speed(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'head_speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'head_speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._head_speed = value

    @builtins.property
    def head_angular_pos(self):
        """Message field 'head_angular_pos'."""
        return self._head_angular_pos

    @head_angular_pos.setter
    def head_angular_pos(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'head_angular_pos' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'head_angular_pos' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._head_angular_pos = value

    @builtins.property
    def drill_rpm(self):
        """Message field 'drill_rpm'."""
        return self._drill_rpm

    @drill_rpm.setter
    def drill_rpm(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'drill_rpm' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'drill_rpm' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._drill_rpm = value

    @builtins.property
    def feed_pressure(self):
        """Message field 'feed_pressure'."""
        return self._feed_pressure

    @feed_pressure.setter
    def feed_pressure(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'feed_pressure' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'feed_pressure' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._feed_pressure = value

    @builtins.property
    def rot_pressure(self):
        """Message field 'rot_pressure'."""
        return self._rot_pressure

    @rot_pressure.setter
    def rot_pressure(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'rot_pressure' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rot_pressure' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rot_pressure = value

    @builtins.property
    def air_pressure(self):
        """Message field 'air_pressure'."""
        return self._air_pressure

    @air_pressure.setter
    def air_pressure(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'air_pressure' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'air_pressure' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._air_pressure = value

    @builtins.property
    def head_pos_is_reliable(self):
        """Message field 'head_pos_is_reliable'."""
        return self._head_pos_is_reliable

    @head_pos_is_reliable.setter
    def head_pos_is_reliable(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'head_pos_is_reliable' field must be of type 'bool'"
        self._head_pos_is_reliable = value
