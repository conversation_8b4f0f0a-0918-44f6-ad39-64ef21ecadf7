// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from drill_msgs:msg/DepthInfo.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "drill_msgs/msg/detail/depth_info__struct.h"
#include "drill_msgs/msg/detail/depth_info__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool drill_msgs__msg__depth_info__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[37];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("drill_msgs.msg._depth_info.DepthInfo", full_classname_dest, 36) == 0);
  }
  drill_msgs__msg__DepthInfo * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // hole_depth
    PyObject * field = PyObject_GetAttrString(_pymsg, "hole_depth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->hole_depth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // current_bit_depth
    PyObject * field = PyObject_GetAttrString(_pymsg, "current_bit_depth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->current_bit_depth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // tvd_hole_depth
    PyObject * field = PyObject_GetAttrString(_pymsg, "tvd_hole_depth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->tvd_hole_depth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // head_pos
    PyObject * field = PyObject_GetAttrString(_pymsg, "head_pos");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->head_pos = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // ground_head_pos
    PyObject * field = PyObject_GetAttrString(_pymsg, "ground_head_pos");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->ground_head_pos = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // wellhead_altitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "wellhead_altitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->wellhead_altitude = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // hole_inclination
    PyObject * field = PyObject_GetAttrString(_pymsg, "hole_inclination");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->hole_inclination = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // target_hole_depth
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_hole_depth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->target_hole_depth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // wellhead_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "wellhead_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->wellhead_x = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // wellhead_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "wellhead_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->wellhead_y = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // tracking_active
    PyObject * field = PyObject_GetAttrString(_pymsg, "tracking_active");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->tracking_active = (Py_True == field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * drill_msgs__msg__depth_info__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of DepthInfo */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("drill_msgs.msg._depth_info");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "DepthInfo");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  drill_msgs__msg__DepthInfo * ros_message = (drill_msgs__msg__DepthInfo *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // hole_depth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->hole_depth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "hole_depth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // current_bit_depth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->current_bit_depth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "current_bit_depth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // tvd_hole_depth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->tvd_hole_depth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "tvd_hole_depth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // head_pos
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->head_pos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "head_pos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // ground_head_pos
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->ground_head_pos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "ground_head_pos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // wellhead_altitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->wellhead_altitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "wellhead_altitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // hole_inclination
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->hole_inclination);
    {
      int rc = PyObject_SetAttrString(_pymessage, "hole_inclination", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_hole_depth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->target_hole_depth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_hole_depth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // wellhead_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->wellhead_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "wellhead_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // wellhead_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->wellhead_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "wellhead_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // tracking_active
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->tracking_active ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "tracking_active", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
