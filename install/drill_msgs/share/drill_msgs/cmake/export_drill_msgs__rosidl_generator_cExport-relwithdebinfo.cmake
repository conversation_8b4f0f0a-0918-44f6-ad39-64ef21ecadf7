#----------------------------------------------------------------
# Generated CMake target import file for configuration "RelWithDebInfo".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "drill_msgs::drill_msgs__rosidl_generator_c" for configuration "RelWithDebInfo"
set_property(TARGET drill_msgs::drill_msgs__rosidl_generator_c APPEND PROPERTY IMPORTED_CONFIGURATIONS RELWITHDEBINFO)
set_target_properties(drill_msgs::drill_msgs__rosidl_generator_c PROPERTIES
  IMPORTED_LOCATION_RELWITHDEBINFO "${_IMPORT_PREFIX}/lib/libdrill_msgs__rosidl_generator_c.dylib"
  IMPORTED_SONAME_RELWITHDEBINFO "@rpath/libdrill_msgs__rosidl_generator_c.dylib"
  )

list(APPEND _cmake_import_check_targets drill_msgs::drill_msgs__rosidl_generator_c )
list(APPEND _cmake_import_check_files_for_drill_msgs::drill_msgs__rosidl_generator_c "${_IMPORT_PREFIX}/lib/libdrill_msgs__rosidl_generator_c.dylib" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
