# DepthInfo.msg
# Полная информация о глубинах бурения с учетом наклонных скважин

std_msgs/Header header

float32 hole_depth                     # Глубина скважины (measured depth) от устья, м (максимальная достигнутая)
float32 current_bit_depth              # Текущая глубина бура в скважине, м (при протяжке < hole_depth)
float32 tvd_hole_depth                 # Истинная вертикальная глубина дна скважины (TVD), м
float32 head_pos                       # Позиция вращателя (шпинделя) от верха мачты, м (DrillState.head_pos)
float32 ground_head_pos                # head_pos при касании земли (опорная), м

float32 wellhead_altitude              # Абсолютная высота (уровень) устья, м (рассчитывается при касании)
float32 hole_inclination               # Угол наклона скважины от вертикали, градусы (из DrillState.head_angular_pos)
float32 target_hole_depth              # Целевая глубина скважины, м
float32 wellhead_x                     # Координата X скважины, m
float32 wellhead_y                     # Координата Y скважины, m

bool tracking_active                   # Активен ли трекинг глубины