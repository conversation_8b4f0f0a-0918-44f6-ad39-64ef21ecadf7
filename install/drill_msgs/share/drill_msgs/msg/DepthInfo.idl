// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/DepthInfo.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    @verbatim (language="comment", text=
      "DepthInfo.msg" "\n"
      "Полная информация о глубинах бурения с учетом наклонных скважин")
    struct DepthInfo {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Глубина скважины (measured depth) от устья, м (максимальная достигнутая)")
      float hole_depth;

      @verbatim (language="comment", text=
        "Текущая глубина бура в скважине, м (при протяжке < hole_depth)")
      float current_bit_depth;

      @verbatim (language="comment", text=
        "Истинная вертикальная глубина дна скважины (TVD), м")
      float tvd_hole_depth;

      @verbatim (language="comment", text=
        "Позиция вращателя (шпинделя) от верха мачты, м (DrillState.head_pos)")
      float head_pos;

      @verbatim (language="comment", text=
        "head_pos при касании земли (опорная), м")
      float ground_head_pos;

      @verbatim (language="comment", text=
        "Абсолютная высота (уровень) устья, м (рассчитывается при касании)")
      float wellhead_altitude;

      @verbatim (language="comment", text=
        "Угол наклона скважины от вертикали, градусы (из DrillState.head_angular_pos)")
      float hole_inclination;

      @verbatim (language="comment", text=
        "Целевая глубина скважины, м")
      float target_hole_depth;

      @verbatim (language="comment", text=
        "Координата X скважины, m")
      float wellhead_x;

      @verbatim (language="comment", text=
        "Координата Y скважины, m")
      float wellhead_y;

      @verbatim (language="comment", text=
        "Активен ли трекинг глубины")
      boolean tracking_active;
    };
  };
};
