// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/StateCommand.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct StateCommand {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Name of the FSM node to target")
      string node_name;

      @verbatim (language="comment", text=
        "State to transition to (or \"prev\" for previous state)")
      string state;
    };
  };
};
