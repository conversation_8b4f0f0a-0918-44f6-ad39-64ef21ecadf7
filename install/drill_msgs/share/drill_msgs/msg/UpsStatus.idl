// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/UpsStatus.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct UpsStatus {
      std_msgs::msg::Header header;

      float batt_current;

      float grid_current;

      float batt_voltage;

      float grid_voltage;

      float charge_current;

      boolean batt_active;

      boolean grid_active;

      boolean batt_voltage_ok;

      boolean grid_voltage_ok;

      boolean batt_current_exceeded;

      boolean grid_current_exceeded;
    };
  };
};
