// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/Position.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct Position {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "meters")
      float x;

      @verbatim (language="comment", text=
        "meters")
      float y;

      @verbatim (language="comment", text=
        "meters")
      float z;

      @verbatim (language="comment", text=
        "rad")
      float yaw;

      boolean is_reliable;
    };
  };
};
