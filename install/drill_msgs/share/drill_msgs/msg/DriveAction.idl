// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/DriveAction.msg
// generated code does not contain a copyright notice

#include "drill_msgs/msg/Path.idl"
#include "drill_msgs/msg/Point2d.idl"
#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct DriveAction {
      std_msgs::msg::Header header;

      int32 id;

      sequence<drill_msgs::msg::Path> path_segments;

      drill_msgs::msg::Point2d hole_position;
    };
  };
};
