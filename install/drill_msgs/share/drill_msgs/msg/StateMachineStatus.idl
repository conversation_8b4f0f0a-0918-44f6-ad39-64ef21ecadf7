// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/StateMachineStatus.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct StateMachineStatus {
      std_msgs::msg::Header header;

      int32 cur_action_id;

      int32 last_action_id;

      string current_state;
    };
  };
};
