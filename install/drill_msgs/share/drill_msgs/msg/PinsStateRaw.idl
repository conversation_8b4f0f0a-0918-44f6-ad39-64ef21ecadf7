// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/PinsStateRaw.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct PinsStateRaw {
      std_msgs::msg::Header header;

      boolean left_open;

      boolean left_locked;

      boolean right_open;

      boolean right_locked;
    };
  };
};
