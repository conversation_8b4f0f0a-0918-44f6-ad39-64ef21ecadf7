// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/TracksState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct TracksState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "m/s")
      float left;

      @verbatim (language="comment", text=
        "m/s")
      float right;

      boolean is_reliable;
    };
  };
};
