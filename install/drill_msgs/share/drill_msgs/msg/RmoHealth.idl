// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/RmoHealth.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    @verbatim (language="comment", text=
      "RMO (Remote Machine Operator) health status message" "\n"
      "Contains information about the control station status and which vehicle is being watched")
    struct RmoHealth {
      @verbatim (language="comment", text=
        "Standard header with timestamp and frame information")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Control station identifier from settings.json")
      uint32 iplace_id;

      @verbatim (language="comment", text=
        "Whether this specific vehicle is being watched by the operator")
      boolean is_watched;

      @verbatim (language="comment", text=
        "ID of the vehicle currently being watched (empty string if none)")
      string watched_vehid;
    };
  };
};
