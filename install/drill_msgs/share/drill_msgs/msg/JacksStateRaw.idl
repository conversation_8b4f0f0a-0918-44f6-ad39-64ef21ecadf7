// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/JacksStateRaw.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct JacksStateRaw {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "V")
      float front_left_len;

      @verbatim (language="comment", text=
        "V")
      float front_right_len;

      @verbatim (language="comment", text=
        "V")
      float rear_left_len;

      @verbatim (language="comment", text=
        "V")
      float rear_right_len;
    };
  };
};
