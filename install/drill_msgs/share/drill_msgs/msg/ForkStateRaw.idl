// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/ForkStateRaw.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct ForkStateRaw {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "V/mA")
      float length;

      boolean cw_limit;

      boolean ccw_limit;
    };
  };
};
