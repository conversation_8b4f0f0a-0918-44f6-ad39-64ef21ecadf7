// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/TowerCtrl.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct TowerCtrl {
      std_msgs::msg::Header header;

      @unit (value="-1..1")
      float tilt;

      @unit (value="-1..1")
      int32 vert_pins;

      @unit (value="-1..1")
      int32 incl_pins;
    };
  };
};
