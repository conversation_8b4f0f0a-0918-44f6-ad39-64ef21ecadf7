// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/SpeedState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct SpeedState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "m/s")
      float forward;

      @verbatim (language="comment", text=
        "m/s")
      float lateral;

      @verbatim (language="comment", text=
        "rad/s")
      float angular;

      boolean is_reliable;
    };
  };
};
