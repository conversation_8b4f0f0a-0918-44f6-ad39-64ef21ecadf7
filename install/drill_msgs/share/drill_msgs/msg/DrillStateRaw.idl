// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/DrillStateRaw.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct DrillStateRaw {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "mA")
      float head_pos;

      @verbatim (language="comment", text=
        "pulses")
      int16 head_angular_pos;

      @verbatim (language="comment", text=
        "V/mA")
      float drill_rpm;

      @verbatim (language="comment", text=
        "V/mA")
      float feed_pressure;

      @verbatim (language="comment", text=
        "V/mA")
      float rot_pressure;

      @verbatim (language="comment", text=
        "V/mA")
      float air_pressure;
    };
  };
};
