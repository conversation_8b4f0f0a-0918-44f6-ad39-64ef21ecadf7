// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/TowerState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct TowerState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "degrees")
      float inclination;

      boolean vert_pin_locked;

      boolean vert_pin_unlocked;

      boolean incl_pin_locked;

      boolean incl_pin_unlocked;
    };
  };
};
