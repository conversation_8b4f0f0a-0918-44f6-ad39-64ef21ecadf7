// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/WrenchStateRaw.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct WrenchStateRaw {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "V")
      float stage_1_len;

      @verbatim (language="comment", text=
        "V")
      float stage_2_len;

      @verbatim (language="comment", text=
        "V")
      float stage_3_len;
    };
  };
};
