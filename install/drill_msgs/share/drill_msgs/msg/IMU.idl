// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/IMU.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Vector3.idl"
#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct IMU {
      std_msgs::msg::Header header;

      float roll;

      float pitch;

      float yaw;

      geometry_msgs::msg::Vector3 angular_rate;

      geometry_msgs::msg::Vector3 acceleration;

      geometry_msgs::msg::Vector3 magnetic_field;
    };
  };
};
