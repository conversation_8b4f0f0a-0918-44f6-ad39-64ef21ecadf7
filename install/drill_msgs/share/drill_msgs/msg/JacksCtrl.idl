// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/JacksCtrl.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct JacksCtrl {
      std_msgs::msg::Header header;

      @unit (value="-1..1")
      float left;

      @unit (value="-1..1")
      float right;

      @unit (value="-1..1")
      float rear;
    };
  };
};
