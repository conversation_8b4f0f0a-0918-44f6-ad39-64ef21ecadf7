// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/LampCtrl.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct LampCtrl {
      std_msgs::msg::Header header;

      boolean front_red;

      boolean front_yellow;

      boolean front_blue;

      boolean rear_red;

      boolean rear_yellow;

      boolean rear_blue;
    };
  };
};
