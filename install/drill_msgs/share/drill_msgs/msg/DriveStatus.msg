std_msgs/Header header

# Current action ID being executed (-1 when no action is active)
int32 cur_action_id

# Last completed action ID  
int32 last_action_id

# Current segment ID within the action
int32 cur_segment_id

# Current point ID within the segment
int32 cur_point_id

# Status string (e.g., "executing", "completed", "failed")
string status

# ACTION COMPLETION LOGIC (unified across all controllers):
# Action is completed when:
#   cur_action_id == -1 (no active action) AND
#   last_action_id == expected_action_id (specific action was completed)
# This pattern ensures reliable action completion detection and is used
# consistently across path_follower, key_controller, carousel_controller, etc.
