// generated from rosidl_adapter/resource/msg.idl.em
// with input from drill_msgs/msg/DrillState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module drill_msgs {
  module msg {
    struct DrillState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "meters")
      float head_pos;

      @verbatim (language="comment", text=
        "meters")
      float head_speed;

      @verbatim (language="comment", text=
        "degrees")
      float head_angular_pos;

      float drill_rpm;

      @verbatim (language="comment", text=
        "bar")
      float feed_pressure;

      @verbatim (language="comment", text=
        "bar")
      float rot_pressure;

      @verbatim (language="comment", text=
        "bar")
      float air_pressure;

      boolean head_pos_is_reliable;
    };
  };
};
