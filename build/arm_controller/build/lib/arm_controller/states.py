from base_node.base_fsm import BaseState


class OpenState(BaseState):
    def __init__(self, node):
        super().__init__(name="open", node=node)

    def on_transition_to(self):
        # Reset action bookkeeping
        if self.node.cur_action_id != -1:
            self.node.last_action_id = self.node.cur_action_id
            self.node.cur_action_id = -1
            self.node.log("Arm OPEN action completed", level=self.node.INFO, event_code=self.node.events.ACTION_COMPLETED)
        self.node.new_action_flag = False
        self.node._arm_ctrl_value = 0.0

    def do_work(self):
        # Restore if open switch lost
        s = self.node.subs.arm_state
        if s is not None and not s.open:
            self.node.log("Restore open", level=self.node.WARN)
            self.node.set_state("opening")
            return

        # Handle close command
        if getattr(self.node, "new_action_flag", False) and not getattr(self.node, "do_open", True):
            self.node.set_state("closing")


class OpeningState(BaseState):
    def __init__(self, node):
        super().__init__(name="opening", node=node)
        self._push_start = None
        self._no_react_start = None

    def on_transition_to(self):
        self._push_start = None
        self._no_react_start = None

    def do_work(self):
        t = self.node.get_current_state_duration()
        self.node._arm_ctrl_value = self.node.open_ctrl

        s = self.node.subs.arm_state
        if s is None:
            return

        # Start/Reset push confirmation timer
        if s.open:
            if self._push_start is None:
                self._push_start = self.node.get_time()
        else:
            self._push_start = None

        # No reaction detection: stays jammed (grip or closed) for too long
        grip_present = bool(self.node.global_params.get("system_flags", {}).get("arm_grip_sensor_present", False))
        jammed = (s.grip if grip_present else s.closed)
        if jammed and self._no_react_start is None:
            self._no_react_start = self.node.get_time()
        if not jammed:
            self._no_react_start = None

        # Timeouts
        if t > self.node.opening_time or (
            self._no_react_start is not None and self.node.get_time() - self._no_react_start > self.node.no_reaction_time
        ):
            self.node.handle_error("Arm open timeout", level=self.node.ERROR, event_code=self.node.events.ARM_OPEN_TIMEOUT)
            self.node.set_state("open")
            return

        # Transition to open after stable confirmation
        if self._push_start is not None and (self.node.get_time() - self._push_start) >= self.node.open_push_time:
            self.node.set_state("open")


class ClosingState(BaseState):
    def __init__(self, node):
        super().__init__(name="closing", node=node)
        self._push_start = None
        self._no_react_start = None

    def on_transition_to(self):
        self._push_start = None
        self._no_react_start = None

    def do_work(self):
        # Safety: head position must be above threshold to allow closing
        ds = self.node.subs.drill_state
        if ds and ds.head_pos > self.node.max_head_pos_to_close:
            self.node.handle_error("Head too low for closing", level=self.node.WARN, event_code=self.node.events.ARM_HEAD_TOO_LOW)
            self.node._arm_ctrl_value = 0.0
            self.node.set_state("opening")
            return

        t = self.node.get_current_state_duration()
        self.node._arm_ctrl_value = self.node.close_ctrl

        s = self.node.subs.arm_state
        if s is None:
            return

        # Determine confirmation policy depending on grip sensor presence
        grip_present = bool(self.node.global_params.get("system_flags", {}).get("arm_grip_sensor_present", False))

        # Start/Reset push confirmation timer
        ok = (s.closed and s.grip) if grip_present else s.closed
        if ok:
            if self._push_start is None:
                self._push_start = self.node.get_time()
        else:
            self._push_start = None

        # No reaction detection – if after no_reaction_time still open
        if s.open and self._no_react_start is None:
            self._no_react_start = self.node.get_time()
        if not s.open:
            self._no_react_start = None

        # Timeouts
        if t > self.node.closing_time or (
            self._no_react_start is not None and self.node.get_time() - self._no_react_start > self.node.no_reaction_time
        ):
            self.node.handle_error("Arm close timeout", level=self.node.ERROR, event_code=self.node.events.ARM_CLOSE_TIMEOUT)
            self.node.set_state("open")
            return

        # Transition to closed after stable confirmation
        if self._push_start is not None:
            need = self.node.close_push_time if grip_present else (self.node.grip_push_time + self.node.close_push_time)
            if (self.node.get_time() - self._push_start) >= need:
                self.node.set_state("closed")


class ClosedState(BaseState):
    def __init__(self, node):
        super().__init__(name="closed", node=node)

    def on_transition_to(self):
        # Reset action bookkeeping
        if self.node.cur_action_id != -1:
            self.node.last_action_id = self.node.cur_action_id
            self.node.cur_action_id = -1
            self.node.log("Arm CLOSE action completed", level=self.node.INFO, event_code=self.node.events.ACTION_COMPLETED)
        self.node.new_action_flag = False
        self.node._arm_ctrl_value = 0.0

    def do_work(self):
        # Safety: if head gets too low while closed – reopen
        ds = self.node.subs.drill_state
        if ds and ds.head_pos > self.node.max_head_pos_to_close:
            self.node.handle_error("Head too low while closed", level=self.node.WARN, event_code=self.node.events.ARM_HEAD_TOO_LOW)
            self.node.set_state("opening")
            return

        # Restore fixation when lost
        s = self.node.subs.arm_state
        grip_present = bool(self.node.global_params.get("system_flags", {}).get("arm_grip_sensor_present", False))
        lost = (not s.closed) or (grip_present and not s.grip) if s is not None else False
        if lost:
            self.node.log("Restore closed", level=self.node.WARN)
            self.node.set_state("closing")
            return

        # Handle open command
        if getattr(self.node, "new_action_flag", False) and getattr(self.node, "do_open", False):
            self.node.set_state("opening")


