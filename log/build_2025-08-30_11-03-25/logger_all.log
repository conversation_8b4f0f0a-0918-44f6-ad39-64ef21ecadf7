[0.234s] DEBUG:colcon:Command line arguments: ['/Users/<USER>/.ros2_venv/bin/colcon', 'build', '--packages-select', 'drill_msgs', '--event-handlers', 'console_cohesion+', '--cmake-args', '-DCMAKE_BUILD_TYPE=RelWithDebInfo']
[0.234s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=['console_cohesion+'], ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['drill_msgs'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=['-DCMAKE_BUILD_TYPE=RelWithDebInfo'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x1063f0e10>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x1063f0550>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x1063f0550>>)
[0.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.381s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.381s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/Users/<USER>/Work/drill2/onboard'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ignore_ament_install'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_pkg']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_pkg'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['colcon_meta']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'colcon_meta'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extensions ['ros']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller) by extension 'ros'
[0.393s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller' with type 'ros.ament_python' and name 'arm_controller'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ignore', 'ignore_ament_install']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore_ament_install'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_pkg']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_pkg'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_meta']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_meta'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ros']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ros'
[0.394s] DEBUG:colcon.colcon_core.package_identification:Package 'src/base_node' with type 'ros.ament_python' and name 'base_node'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ignore', 'ignore_ament_install']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore_ament_install'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_pkg']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_pkg'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_meta']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_meta'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ros']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ros'
[0.395s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_decoder' with type 'ros.ament_python' and name 'can_decoder'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ignore', 'ignore_ament_install']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore_ament_install'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_pkg']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_pkg'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_meta']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_meta'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ros']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ros'
[0.395s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_encoder' with type 'ros.ament_python' and name 'can_encoder'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore_ament_install'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_pkg']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_pkg'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_meta']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_meta'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ros']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ros'
[0.396s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_msgs' with type 'ros.ament_cmake' and name 'can_msgs'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore_ament_install'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_pkg']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_pkg'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_meta']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_meta'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ros']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ros'
[0.397s] DEBUG:colcon.colcon_core.package_identification:Package 'src/depth_tracker' with type 'ros.ament_python' and name 'depth_tracker'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ignore', 'ignore_ament_install']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore_ament_install'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_pkg']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_pkg'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_meta']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_meta'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ros']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ros'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['cmake', 'python']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'cmake'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['python_setup_py']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python_setup_py'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore_ament_install'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_pkg']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_pkg'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_meta']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_meta'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ros']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ros'
[0.397s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_msgs' with type 'ros.ament_cmake' and name 'drill_msgs'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore_ament_install'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_pkg']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_pkg'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_meta']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_meta'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ros']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ros'
[0.398s] DEBUG:colcon.colcon_core.package_identification:Failed to parse potential ROS package manifest in'src/drill_regulator': Error(s) in package 'src/drill_regulator/package.xml':
The manifest contains invalid XML:
not well-formed (invalid token): line 1, column 16
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['cmake', 'python']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'cmake'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python'
[0.398s] DEBUG:colcon.colcon_core.package_identification:Python package in 'src/drill_regulator' passes arguments to the setup() function which requires a different identification extension than 'python'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['python_setup_py']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python_setup_py'
[0.776s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_regulator' with type 'python' and name 'drill_regulator'
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ignore', 'ignore_ament_install']
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore'
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore_ament_install'
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_pkg']
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_pkg'
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_meta']
[0.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_meta'
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ros']
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ros'
[0.777s] DEBUG:colcon.colcon_core.package_identification:Package 'src/driller' with type 'ros.ament_python' and name 'driller'
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['ignore', 'ignore_ament_install']
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ignore'
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ignore_ament_install'
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['colcon_pkg']
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'colcon_pkg'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['colcon_meta']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'colcon_meta'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['ros']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ros'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['cmake', 'python']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'cmake'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'python'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['python_setup_py']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'python_setup_py'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['ignore', 'ignore_ament_install']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ignore'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ignore_ament_install'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['colcon_pkg']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'colcon_pkg'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['colcon_meta']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'colcon_meta'
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['ros']
[0.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ros'
[0.779s] DEBUG:colcon.colcon_core.package_identification:Package 'src/driller-old-ros1/drill_launch_pack' with type 'ros.catkin' and name 'drill_launch_pack'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) by extension 'ignore'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) ignored
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['ignore', 'ignore_ament_install']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ignore'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ignore_ament_install'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['colcon_pkg']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'colcon_pkg'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['colcon_meta']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'colcon_meta'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['ros']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ros'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['cmake', 'python']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'cmake'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'python'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['python_setup_py']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'python_setup_py'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ignore'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ignore_ament_install'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['colcon_pkg']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'colcon_pkg'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['colcon_meta']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'colcon_meta'
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['ros']
[0.779s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ros'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['cmake', 'python']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'cmake'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'python'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['python_setup_py']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'python_setup_py'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ignore', 'ignore_ament_install']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore_ament_install'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_pkg']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_pkg'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_meta']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_meta'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ros']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ros'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['cmake', 'python']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'cmake'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['python_setup_py']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python_setup_py'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ignore', 'ignore_ament_install']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore_ament_install'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_pkg']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_pkg'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_meta']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_meta'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ros']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ros'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['cmake', 'python']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'cmake'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['python_setup_py']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python_setup_py'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extensions ['ignore', 'ignore_ament_install']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extension 'ignore'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) ignored
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ignore', 'ignore_ament_install']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore_ament_install'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_pkg']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_pkg'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_meta']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_meta'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ros']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ros'
[0.781s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/maneuver-builder-cpp' with type 'ros.ament_cmake' and name 'maneuver_builder_cpp'
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ignore', 'ignore_ament_install']
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore'
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore_ament_install'
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_pkg']
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_pkg'
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_meta']
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_meta'
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ros']
[0.781s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ros'
[0.782s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/pydubins' with type 'ros.ament_python' and name 'pydubins'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ignore', 'ignore_ament_install']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore_ament_install'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_pkg']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_pkg'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_meta']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_meta'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ros']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ros'
[0.782s] DEBUG:colcon.colcon_core.package_identification:Package 'src/hal_connector' with type 'ros.ament_python' and name 'hal_connector'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ignore', 'ignore_ament_install']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore_ament_install'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_pkg']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_pkg'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_meta']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_meta'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ros']
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ros'
[0.784s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launchpack' with type 'ros.ament_python' and name 'launchpack'
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ignore', 'ignore_ament_install']
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore'
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore_ament_install'
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_pkg']
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_pkg'
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_meta']
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_meta'
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ros']
[0.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ros'
[0.785s] DEBUG:colcon.colcon_core.package_identification:Package 'src/leveler' with type 'ros.ament_python' and name 'leveler'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extensions ['ignore', 'ignore_ament_install']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extension 'ignore'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) ignored
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ignore', 'ignore_ament_install']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore_ament_install'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_pkg']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_pkg'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_meta']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_meta'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ros']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ros'
[0.785s] DEBUG:colcon.colcon_core.package_identification:Package 'src/main_state_machine' with type 'ros.ament_python' and name 'main_state_machine'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ignore', 'ignore_ament_install']
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore_ament_install'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_pkg']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_pkg'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_meta']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_meta'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ros']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ros'
[0.786s] DEBUG:colcon.colcon_core.package_identification:Package 'src/modbus_node' with type 'ros.ament_python' and name 'modbus_node'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) by extensions ['ignore', 'ignore_ament_install']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) by extension 'ignore'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/old-controllers-ros1) ignored
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ignore', 'ignore_ament_install']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore_ament_install'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_pkg']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_pkg'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_meta']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_meta'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ros']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ros'
[0.787s] DEBUG:colcon.colcon_core.package_identification:Package 'src/params_server' with type 'ros.ament_python' and name 'params_server'
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ignore', 'ignore_ament_install']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore'
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore_ament_install'
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_pkg']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_pkg'
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_meta']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_meta'
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ros']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ros'
[0.788s] DEBUG:colcon.colcon_core.package_identification:Package 'src/path_follower' with type 'ros.ament_python' and name 'path_follower'
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ignore', 'ignore_ament_install']
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore'
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore_ament_install'
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_pkg']
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_pkg'
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_meta']
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_meta'
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ros']
[0.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ros'
[0.789s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remote_connector' with type 'ros.ament_python' and name 'remote_connector'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ignore', 'ignore_ament_install']
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore_ament_install'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_pkg']
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_pkg'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_meta']
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_meta'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ros']
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ros'
[0.793s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros-foxglove-bridge' with type 'ros.ament_cmake' and name 'foxglove_bridge'
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ignore', 'ignore_ament_install']
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore'
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore_ament_install'
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_pkg']
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_pkg'
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_meta']
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_meta'
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ros']
[0.794s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ros'
[0.803s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosx_introspection' with type 'ros.ament_cmake' and name 'rosx_introspection'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ignore', 'ignore_ament_install']
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore_ament_install'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_pkg']
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_pkg'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_meta']
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_meta'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ros']
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ros'
[0.804s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rtk_connector' with type 'ros.ament_python' and name 'rtk_connector'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore_ament_install'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_pkg']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_pkg'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_meta']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_meta'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ros']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ros'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['cmake', 'python']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'cmake'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['python_setup_py']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python_setup_py'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore_ament_install'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_pkg']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_pkg'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_meta']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_meta'
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ros']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ros'
[0.805s] DEBUG:colcon.colcon_core.package_identification:Package 'src/state_tracker' with type 'ros.ament_python' and name 'state_tracker'
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore'
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore_ament_install'
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_pkg']
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_pkg'
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_meta']
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_meta'
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ros']
[0.805s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ros'
[0.806s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tracks_regulator' with type 'ros.ament_python' and name 'tracks_regulator'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ignore', 'ignore_ament_install']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore_ament_install'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_pkg']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_pkg'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_meta']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_meta'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ros']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ros'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['cmake', 'python']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'cmake'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['python_setup_py']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python_setup_py'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ignore', 'ignore_ament_install']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore_ament_install'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_pkg']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_pkg'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_meta']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_meta'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ros']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ros'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['cmake', 'python']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'cmake'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['python_setup_py']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python_setup_py'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extensions ['ignore', 'ignore_ament_install']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extension 'ignore'
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) ignored
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extensions ['ignore', 'ignore_ament_install']
[0.806s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extension 'ignore'
[0.807s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) ignored
[0.807s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extensions ['ignore', 'ignore_ament_install']
[0.807s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extension 'ignore'
[0.807s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) ignored
[0.807s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.807s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.807s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.807s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.807s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_msgs' in 'src/can_msgs'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'drill_launch_pack' in 'src/driller-old-ros1/drill_launch_pack'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'drill_regulator' in 'src/drill_regulator'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'launchpack' in 'src/launchpack'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'maneuver_builder_cpp' in 'src/external/maneuver-builder-cpp'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pydubins' in 'src/external/pydubins'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rosx_introspection' in 'src/rosx_introspection'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'base_node' in 'src/base_node'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_decoder' in 'src/can_decoder'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_encoder' in 'src/can_encoder'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'foxglove_bridge' in 'src/ros-foxglove-bridge'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'main_state_machine' in 'src/main_state_machine'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'params_server' in 'src/params_server'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'remote_connector' in 'src/remote_connector'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtk_connector' in 'src/rtk_connector'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'state_tracker' in 'src/state_tracker'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'arm_controller' in 'src/arm_controller'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'depth_tracker' in 'src/depth_tracker'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'driller' in 'src/driller'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hal_connector' in 'src/hal_connector'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leveler' in 'src/leveler'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'modbus_node' in 'src/modbus_node'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'path_follower' in 'src/path_follower'
[0.845s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'tracks_regulator' in 'src/tracks_regulator'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo']'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_target' from command line to 'None'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.846s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.846s] DEBUG:colcon.colcon_core.verb:Building package 'drill_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/Users/<USER>/Work/drill2/onboard/build/drill_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs', 'merge_install': False, 'path': '/Users/<USER>/Work/drill2/onboard/src/drill_msgs', 'symlink_install': False, 'test_result_base': None}
[0.846s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.846s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.847s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/Users/<USER>/Work/drill2/onboard/src/drill_msgs' with build type 'ament_cmake'
[0.847s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/Users/<USER>/Work/drill2/onboard/src/drill_msgs'
[0.851s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.851s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.851s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.172s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake /Users/<USER>/Work/drill2/onboard/src/drill_msgs -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/Users/<USER>/Work/drill2/onboard/install/drill_msgs
[19.045s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake /Users/<USER>/Work/drill2/onboard/src/drill_msgs -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/Users/<USER>/Work/drill2/onboard/install/drill_msgs
[19.046s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/drill_msgs -- -j8 -l8
[249.526s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/drill_msgs -- -j8 -l8
[249.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --install /Users/<USER>/Work/drill2/onboard/build/drill_msgs
[251.924s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(drill_msgs)
[251.926s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/driller_node:/Users/<USER>/Work/drill2/onboard/install/driller:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format SHLVL=3 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --install /Users/<USER>/Work/drill2/onboard/build/drill_msgs
[251.935s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake module files
[251.935s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake config files
[251.936s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'cmake_prefix_path')
[251.937s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.ps1'
[251.937s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.dsv'
[251.938s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.sh'
[251.938s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib'
[251.939s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'dyld_library_path')
[251.939s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.ps1'
[251.939s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.dsv'
[251.939s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.sh'
[251.939s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[251.939s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/pkgconfig/drill_msgs.pc'
[251.940s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages'
[251.940s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'pythonpath')
[251.940s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.ps1'
[251.940s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.dsv'
[251.940s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.sh'
[251.941s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[251.941s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.ps1'
[251.941s] INFO:colcon.colcon_core.shell:Creating package descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.dsv'
[251.942s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.sh'
[251.945s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.bash'
[251.946s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.zsh'
[251.947s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/colcon-core/packages/drill_msgs)
[251.948s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(drill_msgs)
[251.948s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake module files
[251.948s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake config files
[251.950s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'cmake_prefix_path')
[251.950s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.ps1'
[251.950s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.dsv'
[251.951s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.sh'
[251.951s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib'
[251.951s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'dyld_library_path')
[251.951s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.ps1'
[251.951s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.dsv'
[251.952s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.sh'
[251.953s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[251.953s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/pkgconfig/drill_msgs.pc'
[251.953s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages'
[251.953s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'pythonpath')
[251.953s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.ps1'
[251.953s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.dsv'
[251.953s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.sh'
[251.954s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[251.954s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.ps1'
[251.954s] INFO:colcon.colcon_core.shell:Creating package descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.dsv'
[251.955s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.sh'
[251.955s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.bash'
[251.955s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.zsh'
[251.955s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/colcon-core/packages/drill_msgs)
[251.957s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[251.957s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[251.957s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[251.957s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[252.066s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[252.066s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Not used on non-Linux systems
[252.066s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[252.066s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'terminal_notifier'
[252.288s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[252.289s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.ps1'
[252.290s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_ps1.py'
[252.292s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.ps1'
[252.358s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.sh'
[252.359s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_sh.py'
[252.359s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.sh'
[252.374s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.bash'
[252.375s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.bash'
[252.392s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.zsh'
[252.394s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.zsh'
