running egg_info
writing ../../build/arm_controller/arm_controller.egg-info/PKG-INFO
writing dependency_links to ../../build/arm_controller/arm_controller.egg-info/dependency_links.txt
writing entry points to ../../build/arm_controller/arm_controller.egg-info/entry_points.txt
writing requirements to ../../build/arm_controller/arm_controller.egg-info/requires.txt
writing top-level names to ../../build/arm_controller/arm_controller.egg-info/top_level.txt
reading manifest file '../../build/arm_controller/arm_controller.egg-info/SOURCES.txt'
writing manifest file '../../build/arm_controller/arm_controller.egg-info/SOURCES.txt'
running build
running build_py
copying arm_controller/__init__.py -> /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller
copying arm_controller/arm_controller_node.py -> /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller
copying arm_controller/states.py -> /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller
running install
running install_lib
copying /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller/__init__.py -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller
copying /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller/arm_controller_node.py -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller
copying /Users/<USER>/Work/drill2/onboard/build/arm_controller/build/lib/arm_controller/states.py -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller
byte-compiling /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller/__init__.py to __init__.cpython-311.pyc
byte-compiling /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller/arm_controller_node.py to arm_controller_node.cpython-311.pyc
byte-compiling /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller/states.py to states.cpython-311.pyc
running install_data
copying resource/arm_controller -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/share/ament_index/resource_index/packages
copying package.xml -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/share/arm_controller
copying launch/arm_controller.launch.xml -> /Users/<USER>/Work/drill2/onboard/install/arm_controller/share/arm_controller/launch
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller-0.0.1-py3.11.egg-info' (and everything under it)
Copying ../../build/arm_controller/arm_controller.egg-info to /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/python3.11/site-packages/arm_controller-0.0.1-py3.11.egg-info
running install_scripts
Installing arm_controller script to /Users/<USER>/Work/drill2/onboard/install/arm_controller/lib/arm_controller
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/arm_controller/install.log'
