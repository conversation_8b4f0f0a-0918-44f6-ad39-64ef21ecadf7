from rclpy.qos import QoSProfile
from base_node.base_fsm import BaseFSM, BaseState
from drill_msgs.msg import ArmState, DrillState, FloatCtrl, OpenCloseAction, StateMachineStatus


class ArmControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="arm_controller")

        # Publishers
        self._ctrl_pub = self.create_publisher(FloatCtrl, "/arm_ctrl", QoSProfile(depth=10))
        self._arm_ctrl_value: float = 0.0

        # Subscribers with timeouts
        self.add_subscriber("/arm_state", ArmState, "arm_state", timeout=1.0)
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=1.0)
        self.add_subscriber("/main_state_machine_status", StateMachineStatus, "msm", timeout=2.0)

        # Command subscription
        self.create_subscription(OpenCloseAction, "/arm_action", self._on_action, QoSProfile(depth=10))

        # Action bookkeeping
        self.new_action_flag = False
        self.do_open = False

        # Params
        self._load_params()

        # Internal timers
        self._inconsistent_since = None  # debouncer for open+closed inconsistency

        # States
        from .states import OpenState, OpeningState, ClosingState, ClosedState
        self.add_states(OpenState(self), OpeningState(self), ClosingState(self), ClosedState(self))
        self.set_state("open")

    # ---------------------------------------------------------------
    # Params
    def _load_params(self):
        p = self.node_params
        self.rate = p.get("rate", 10.0)
        self.opening_time = p.get("opening_time", 30.0)
        self.closing_time = p.get("closing_time", 30.0)
        self.open_push_time = p.get("open_push_time", 0.5)
        self.close_push_time = p.get("close_push_time", 0.5)
        self.grip_push_time = p.get("grip_push_time", 1.5)
        self.no_reaction_time = p.get("no_reaction_time", 5.5)
        self.max_head_pos_to_close = p.get("max_head_pos_to_close", 13.0)
        self.open_ctrl = p.get("open_ctrl", -1.0)
        self.close_ctrl = p.get("close_ctrl", 1.0)
        self.allowed_modes = set(p.get("allowed_modes", []))
        self.switch_inconsistent_time = p.get("switch_inconsistent_time", 0.3)

    def on_params_update(self, interesting_updated_keys):
        self._load_params()
        self.log("ArmController params updated", level=self.INFO)

    # ---------------------------------------------------------------
    # Required overrides
    def stop_control(self) -> None:
        self._arm_ctrl_value = 0.0
        self._publish_control()

    def safety_check(self) -> bool:
        # Arm presence
        if not self.global_params.get("system_flags", {}).get("arm_present", True):
            if self.current_state and self.current_state.name != "open":
                self.set_state("open")
            return False

        # Drill state validity
        if self.subs.drill_state is None or not getattr(self.subs.drill_state, "head_pos_is_reliable", False):
            return False

        # Arm switches consistency (debounced)
        if self.subs.arm_state is not None:
            s = self.subs.arm_state
            inconsistent = bool(getattr(s, "open", False) and getattr(s, "closed", False))
            now = self.get_time()
            if inconsistent:
                if self._inconsistent_since is None:
                    self._inconsistent_since = now
                elif now - self._inconsistent_since >= float(self.switch_inconsistent_time):
                    self.handle_error(
                        "Inconsistent arm limit switches (debounced)",
                        level=self.ERROR,
                        event_code=self.events.SENSOR_FAILURE,
                    )
                    return False
            else:
                self._inconsistent_since = None

        # Allowed modes gate (if configured)
        # Allowed modes gate (allow work only in allowed modes when there is a pending action)
        if self.allowed_modes and self.new_action_flag:
            msm = getattr(self.subs, "msm", None)
            if msm is None or msm.current_state not in self.allowed_modes:
                return False
        return True

    # ---------------------------------------------------------------
    # Helpers
    def _publish_control(self):
        msg = FloatCtrl()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.ctrl = float(self._arm_ctrl_value)
        self._ctrl_pub.publish(msg)

    def do_work_finally(self) -> None:
        self._publish_control()

    # ---------------------------------------------------------------
    # Command handling
    def _on_action(self, msg: OpenCloseAction):
        cur = self.current_state.name if self.current_state else ""
        if msg.action not in (OpenCloseAction.OPEN, OpenCloseAction.CLOSE):
            self.handle_error("Invalid arm action", level=self.WARN, event_code=self.events.ACTION_NOT_ACCEPTED)
            return

        if (msg.action == OpenCloseAction.OPEN and cur == "open") or (msg.action == OpenCloseAction.CLOSE and cur == "closed"):
            self.log("Arm already in requested state", level=self.INFO)
            return

        if msg.action == OpenCloseAction.OPEN and cur in ("closed", "closing"):
            self.new_action_flag = True
            self.do_open = True
            self.cur_action_id = msg.id
            if cur == "closed":
                self.set_state("opening")
        elif msg.action == OpenCloseAction.CLOSE and cur in ("open", "opening"):
            self.new_action_flag = True
            self.do_open = False
            self.cur_action_id = msg.id
            if cur == "open":
                self.set_state("closing")
        else:
            self.handle_error(f"Invalid command or state. action={msg.action}, state={cur}", level=self.WARN, event_code=self.events.ACTION_NOT_ACCEPTED)


def main():
    import rclpy
    rclpy.init()
    node = ArmControllerFSM()
    node.run()


