# Контроллер люнета (arm_controller)

Назначение: управление открытием/закрытием люнета с контролем безопасности. Реализация на ROS 2 с использованием `BaseFSM`.

## Интерфейсы

- Входы
  - `/arm_action` — `drill_msgs/OpenCloseAction` (поля: `id:int32`, `action:int32` = {OPEN=1, CLOSE=-1})
  - `/arm_state` — `drill_msgs/ArmState` (поля: `open:bool`, `closed:bool`, `grip:bool`)
  - `/drill_state` — `drill_msgs/DrillState` (поля: `head_pos:float32`, `head_pos_is_reliable:bool`)
  - Авто-входы `BaseFSM`: `/set_state` (`StateCommand`), `/permission` (`Permission`), `/robomode` (`BoolStamped`), `/main_state_machine_status` (`StateMachineStatus`)

- Выходы
  - `/arm_ctrl` — `drill_msgs/FloatCtrl` (отрицательные — открыть, положительные — закрыть, 0 — стоп)
  - `/arm_controller_status` — `drill_msgs/StateMachineStatus` (поля: `cur_action_id`, `last_action_id`, `current_state`)

## Команды и завершение заданий

- Команда: публиковать `OpenCloseAction{id, action}` в `/arm_action`.
- Завершение: задание завершено, если `cur_action_id == -1` и `last_action_id == expected_id`.

## Состояния FSM

- `open` (начальное)
  - Управление: `ctrl=0`.
  - Восстановление: при `open==False` → `opening` ("Restore open").
  - Команда закрытия: при новом действии закрытия → `closing`.
  - Сброс идентификаторов задания и событие `ACTION_COMPLETED` при входе.

- `opening`
  - Управление: `FloatCtrl(ctrl=open_ctrl)`.
  - Таймаут: `get_current_state_duration() > opening_time` → `ARM_OPEN_TIMEOUT`.
  - Отсутствие реакции: по `no_reaction_time`, критерий застревания — `grip` (если есть) иначе `closed`.
  - Подтверждение: переход в `open` после непрерывного `open==True` в течение `open_push_time`.

- `closing`
  - Безопасность по глубине: при `head_pos > max_head_pos_to_close` → остановка, событие `ARM_HEAD_TOO_LOW`, переход в `opening`.
  - Управление: `FloatCtrl(ctrl=close_ctrl)`.
  - Таймаут: `> closing_time` → `ARM_CLOSE_TIMEOUT`.
  - Отсутствие реакции: если `open==True` дольше `no_reaction_time` → `ARM_CLOSE_TIMEOUT`.
  - Подтверждение: если `arm_grip_sensor_present==True` — нужно `closed && grip` в течение `close_push_time`; иначе — `closed` в течение `grip_push_time + close_push_time`.

- `closed`
  - Управление: `ctrl=0`.
  - Защита по глубине: при `head_pos > max_head_pos_to_close` → `ARM_HEAD_TOO_LOW`, переход в `opening`.
  - Восстановление фиксации: при потере `closed` или (при наличии датчика) `grip` → `closing` ("Restore closed").
  - Команда открытия: при новом действии открытия → `opening`.
  - Сброс идентификаторов задания и событие `ACTION_COMPLETED` при входе.

## Безопасность и дополнительные проверки

- `arm_present=false` (Global) — автоматический переход в `open` и остановка работы.
- Валидность бурения: требуется `head_pos_is_reliable==True`.
- Неконсистентность концевиков (`open && closed`) — дебаунс по `switch_inconsistent_time`, затем событие `SENSOR_FAILURE` и блокировка.
- Режимы: при наличии `allowed_modes` работа по команде разрешена только когда текущий режим в списке.

## Параметры (nodes.yaml → `arm_controller`)

- `rate` (Гц), `opening_time`, `closing_time`, `open_push_time`, `close_push_time`, `grip_push_time`, `no_reaction_time`, `max_head_pos_to_close`, `open_ctrl`, `close_ctrl`, `allowed_modes[]`, `switch_inconsistent_time`.
- Параметры обновляются онлайн через самописный сервер параметров (реакция в `on_params_update`).

## События (event_codes)

- `ACTION_NOT_ACCEPTED`, `ACTION_COMPLETED`, `ARM_OPEN_TIMEOUT`, `ARM_CLOSE_TIMEOUT`, `ARM_HEAD_TOO_LOW`, `SENSOR_FAILURE`.

## Запуск

- Launch: `share/arm_controller/launch/arm_controller.launch.xml`
- Окружение параметр-сервера: `PARAM_SERVER_ADDRESS`, `PARAM_SERVER_PORT`, `PARAM_SERVER_LOGLEVEL`.

## Соответствие заданию

- Входы/выходы, таймауты и состояния — в полном соответствии с `task-arm-controller.md`.
- Логика безопасности и автопереходы совпадают с ROS1-версией (не хуже), учитывая наличие/отсутствие датчика `grip`.
- Статусы заданий и публикация событий реализованы.
- Поддержка динамического обновления параметров — реализована.
